[Unit]
Description=Auto Scouter Celery Worker
Documentation=https://github.com/your-org/auto-scouter
After=network.target redis.service postgresql.service auto-scouter-api.service
Wants=redis.service postgresql.service
Requires=network.target

[Service]
Type=exec
User=autoscouter
Group=autoscouter
WorkingDirectory=/opt/auto-scouter/backend
Environment=PATH=/opt/auto-scouter/backend/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/auto-scouter/backend
Environment=ENVIRONMENT=production
EnvironmentFile=/opt/auto-scouter/backend/.env

# Celery worker with multiple queues
ExecStart=/opt/auto-scouter/backend/venv/bin/celery \
    --app=app.core.celery_app \
    worker \
    --loglevel=info \
    --concurrency=4 \
    --queues=scraping,alert_matching,notifications,maintenance,default \
    --hostname=worker@%%h \
    --logfile=/var/log/auto-scouter/worker.log \
    --pidfile=/var/run/auto-scouter/worker.pid

# Health check
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c '/opt/auto-scouter/backend/venv/bin/celery --app=app.core.celery_app inspect ping -d worker@$HOSTNAME || exit 1'

# Graceful shutdown
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=60

# Restart configuration
Restart=always
RestartSec=15
StartLimitInterval=120
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/auto-scouter /var/log/auto-scouter /var/run/auto-scouter /tmp

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=auto-scouter-worker

[Install]
WantedBy=multi-user.target
