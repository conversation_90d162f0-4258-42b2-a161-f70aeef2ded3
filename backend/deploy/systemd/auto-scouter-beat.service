[Unit]
Description=Auto Scouter Celery Beat Scheduler
Documentation=https://github.com/your-org/auto-scouter
After=network.target redis.service postgresql.service auto-scouter-worker.service
Wants=redis.service postgresql.service auto-scouter-worker.service
Requires=network.target

[Service]
Type=exec
User=autoscouter
Group=autoscouter
WorkingDirectory=/opt/auto-scouter/backend
Environment=PATH=/opt/auto-scouter/backend/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/auto-scouter/backend
Environment=ENVIRONMENT=production
EnvironmentFile=/opt/auto-scouter/backend/.env

# Celery beat scheduler
ExecStart=/opt/auto-scouter/backend/venv/bin/celery \
    --app=app.core.celery_app \
    beat \
    --loglevel=info \
    --logfile=/var/log/auto-scouter/beat.log \
    --pidfile=/var/run/auto-scouter/beat.pid \
    --schedule=/var/lib/auto-scouter/celerybeat-schedule

# Ensure only one beat instance runs
ExecStartPre=/bin/rm -f /var/run/auto-scouter/beat.pid
ExecStartPre=/bin/rm -f /var/lib/auto-scouter/celerybeat-schedule.db

# Health check
ExecStartPost=/bin/sleep 5
ExecStartPost=/bin/bash -c 'test -f /var/run/auto-scouter/beat.pid || exit 1'

# Graceful shutdown
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Restart configuration
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/auto-scouter /var/log/auto-scouter /var/run/auto-scouter /var/lib/auto-scouter /tmp

# Resource limits
LimitNOFILE=65536
LimitNPROC=1024

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=auto-scouter-beat

[Install]
WantedBy=multi-user.target
