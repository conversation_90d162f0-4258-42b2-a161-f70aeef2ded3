# Auto Scouter Production Environment Configuration

# Application Settings
PROJECT_NAME="Auto Scouter"
VERSION="1.0.0"
API_V1_STR="/api/v1"
DEBUG=false
ENVIRONMENT="production"

# Database Configuration
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/auto_scouter_prod"
SQLITE_FALLBACK=false

# Redis/Celery Configuration
REDIS_URL="redis://localhost:6379/0"
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"

# Security Settings
SECRET_KEY="your-super-secret-key-change-this-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
ALLOWED_METHODS="GET,POST,PUT,DELETE,OPTIONS"
ALLOWED_HEADERS="*"

# Scraping Configuration
SCRAPING_ENABLED=true
SCRAPING_INTERVAL_HOURS=0.083  # 5 minutes
SCRAPING_MAX_VEHICLES_PER_RUN=100
SCRAPING_DELAY_BETWEEN_REQUESTS=2
SCRAPING_TIMEOUT_SECONDS=30
SCRAPING_USER_AGENT="Auto Scouter Bot 1.0"

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"
LOG_FILE_PATH="/var/log/auto_scouter/app.log"
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Monitoring & Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_USE_TLS=true
EMAIL_FROM="<EMAIL>"

# Notification Settings
NOTIFICATION_QUEUE_SIZE=1000
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=60

# Data Retention
DATA_RETENTION_DAYS=90
NOTIFICATION_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7

# Performance Settings
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
MAX_REQUEST_SIZE=10485760  # 10MB

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CONTENT_SECURITY_POLICY="default-src 'self'"

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET="auto-scouter-backups"

# External Services
WEBHOOK_SECRET="your-webhook-secret"
API_KEY_ENCRYPTION_KEY="your-encryption-key"

# Feature Flags
FEATURE_REAL_TIME_NOTIFICATIONS=true
FEATURE_ADVANCED_FILTERING=true
FEATURE_ANALYTICS=true
FEATURE_EXPORT=true
