{"name": "petrit-vehicle-scout-backend", "version": "1.0.0", "description": "Real-time vehicle alert system backend for Petrit's Vehicle Scout", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "scrape": "node scripts/test-scraper.js"}, "keywords": ["vehicle", "scraping", "alerts", "real-time", "websocket", "gruppo-auto-uno"], "author": "Petrit's Vehicle Scout", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "ws": "^8.14.2", "geolib": "^3.3.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}