# Auto Scouter Docker Compose for Windows
# Run with: docker-compose -f docker-compose.windows.yml up -d

version: '3.8'

services:
  # Redis for caching and message broker
  redis:
    image: redis:7-alpine
    container_name: auto-scouter-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: auto-scouter-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: auto_scouter_prod
      POSTGRES_USER: autoscouter
      POSTGRES_PASSWORD: auto_scouter_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

  # Auto Scouter API
  auto-scouter-api:
    build:
      context: .
      dockerfile: Dockerfile.windows
    container_name: auto-scouter-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*******************************************************************/auto_scouter_prod
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - postgres
      - redis
    command: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

  # Celery Worker
  auto-scouter-worker:
    build:
      context: .
      dockerfile: Dockerfile.windows
    container_name: auto-scouter-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=*******************************************************************/auto_scouter_prod
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - postgres
      - redis
    command: python -m celery -A app.core.celery_app worker --loglevel=info --concurrency=4

  # Celery Beat Scheduler
  auto-scouter-beat:
    build:
      context: .
      dockerfile: Dockerfile.windows
    container_name: auto-scouter-beat
    restart: unless-stopped
    environment:
      - DATABASE_URL=*******************************************************************/auto_scouter_prod
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
      - beat_data:/app/beat
    depends_on:
      - postgres
      - redis
    command: python -m celery -A app.core.celery_app beat --loglevel=info

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: auto-scouter-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/nginx/auto-scouter.conf:/etc/nginx/conf.d/default.conf
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - auto-scouter-api

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  beat_data:
    driver: local
