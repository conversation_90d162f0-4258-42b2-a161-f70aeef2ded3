"""
Enhanced Alert Management API Endpoints

This module provides comprehensive REST API endpoints for alert management
with advanced filtering, testing, and analytics.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func, or_
from typing import List, Optional
from datetime import datetime, timedelta

from app.models.base import get_db
from app.models.scout import Alert, User
from app.models.automotive import VehicleListing
from app.models.notifications import Notification, AlertMatchLog
from app.schemas.alerts import (
    AlertCreate, AlertUpdate, AlertResponse, AlertStats,
    AlertTestRequest, AlertTestResponse
)
from app.core.auth import get_current_active_user
from app.services.enhanced_alert_matcher import EnhancedAlertMatchingEngine
from app.tasks.alert_matching import match_single_alert_task

router = APIRouter()


@router.get("/", response_model=dict)
def get_user_alerts(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user's alerts with pagination"""
    try:
        # Build query
        query = db.query(Alert).filter(Alert.user_id == current_user.id)

        # Apply filters
        if is_active is not None:
            query = query.filter(Alert.is_active == is_active)

        # Get total count
        total_count = query.count()

        # Apply pagination
        offset = (page - 1) * page_size
        alerts = query.order_by(desc(Alert.created_at)).offset(offset).limit(page_size).all()

        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size

        return {
            "alerts": alerts,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve alerts: {str(e)}"
        )


@router.post("/", response_model=AlertResponse)
def create_alert(
    alert_data: AlertCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new alert"""
    try:
        # Validate alert criteria
        if not any([
            alert_data.make, alert_data.model, alert_data.min_price, 
            alert_data.max_price, alert_data.min_year, alert_data.max_year
        ]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one search criteria must be specified"
            )

        # Create alert
        alert = Alert(
            user_id=current_user.id,
            **alert_data.dict()
        )

        db.add(alert)
        db.commit()
        db.refresh(alert)

        return alert

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create alert: {str(e)}"
        )


@router.get("/{alert_id}", response_model=AlertResponse)
def get_alert(
    alert_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific alert"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    return alert


@router.put("/{alert_id}", response_model=AlertResponse)
def update_alert(
    alert_id: int,
    alert_update: AlertUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update an alert"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    # Update alert fields
    update_data = alert_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(alert, field, value)

    alert.updated_at = datetime.utcnow()

    db.commit()
    db.refresh(alert)

    return alert


@router.delete("/{alert_id}")
def delete_alert(
    alert_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete an alert"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    db.delete(alert)
    db.commit()

    return {"message": "Alert deleted successfully"}


@router.post("/{alert_id}/toggle")
def toggle_alert_status(
    alert_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Toggle alert active status"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    alert.is_active = not alert.is_active
    alert.updated_at = datetime.utcnow()

    db.commit()

    return {
        "message": f"Alert {'activated' if alert.is_active else 'deactivated'} successfully",
        "is_active": alert.is_active
    }


@router.post("/{alert_id}/test", response_model=AlertTestResponse)
def test_alert(
    alert_id: int,
    test_request: AlertTestRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Test an alert against current listings"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    try:
        # Get recent listings to test against
        since = datetime.utcnow() - timedelta(days=test_request.test_days)
        listings = db.query(VehicleListing).filter(
            VehicleListing.scraped_at >= since,
            VehicleListing.is_active == True
        ).limit(test_request.max_listings).all()

        # Test alert matching
        matcher = EnhancedAlertMatchingEngine(db)
        matches = []
        
        for listing in listings:
            match_result = matcher.check_alert_match(alert, listing)
            if match_result:
                matches.append({
                    "listing_id": listing.id,
                    "listing": {
                        "make": listing.make,
                        "model": listing.model,
                        "year": listing.year,
                        "price": listing.price,
                        "city": listing.city
                    },
                    "match_score": match_result["match_score"],
                    "matched_criteria": match_result["matched_criteria"],
                    "is_perfect_match": match_result["is_perfect_match"]
                })

        return {
            "alert_id": alert_id,
            "test_period_days": test_request.test_days,
            "listings_tested": len(listings),
            "matches_found": len(matches),
            "matches": matches[:10],  # Return top 10 matches
            "would_trigger": len(matches) > 0
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Alert test failed: {str(e)}"
        )


@router.get("/{alert_id}/stats", response_model=AlertStats)
def get_alert_stats(
    alert_id: int,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get statistics for a specific alert"""
    alert = db.query(Alert).filter(
        and_(
            Alert.id == alert_id,
            Alert.user_id == current_user.id
        )
    ).first()

    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )

    since_date = datetime.utcnow() - timedelta(days=days)

    # Get notification count
    notification_count = db.query(Notification).filter(
        and_(
            Notification.alert_id == alert_id,
            Notification.created_at >= since_date
        )
    ).count()

    # Get recent notifications
    recent_notifications = db.query(Notification).filter(
        Notification.alert_id == alert_id
    ).order_by(desc(Notification.created_at)).limit(5).all()

    return {
        "alert_id": alert_id,
        "alert_name": alert.name,
        "is_active": alert.is_active,
        "created_at": alert.created_at,
        "last_triggered": alert.last_triggered,
        "trigger_count": alert.trigger_count,
        "notifications_in_period": notification_count,
        "recent_notifications": recent_notifications,
        "period_days": days
    }
