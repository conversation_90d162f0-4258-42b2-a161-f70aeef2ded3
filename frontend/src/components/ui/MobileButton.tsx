import React from 'react';
import { MobileUtils } from '@/utils/mobile';
import { cn } from '@/lib/utils';

interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  hapticFeedback?: 'light' | 'medium' | 'heavy' | 'none';
  children: React.ReactNode;
}

const buttonVariants = {
  default: 'bg-primary text-primary-foreground hover:bg-primary/90',
  destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  ghost: 'hover:bg-accent hover:text-accent-foreground',
  link: 'text-primary underline-offset-4 hover:underline',
};

const buttonSizes = {
  default: 'h-10 px-4 py-2',
  sm: 'h-9 rounded-md px-3',
  lg: 'h-11 rounded-md px-8',
  icon: 'h-10 w-10',
};

export const MobileButton: React.FC<MobileButtonProps> = ({
  className,
  variant = 'default',
  size = 'default',
  hapticFeedback = 'light',
  onClick,
  children,
  ...props
}) => {
  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    // Provide haptic feedback on mobile
    if (hapticFeedback !== 'none') {
      await MobileUtils.provideFeedback(hapticFeedback);
    }

    // Call the original onClick handler
    if (onClick) {
      onClick(event);
    }
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        buttonVariants[variant],
        buttonSizes[size],
        'touch-target touch-feedback', // Mobile-specific classes
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {children}
    </button>
  );
};
