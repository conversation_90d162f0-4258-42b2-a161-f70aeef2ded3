# Petrit's Vehicle Scout

A personalized React application for intelligent vehicle search and alert management. Built with React 18, TypeScript, and modern web technologies. **Customized for single-user personal use.**

This application can run as both a web application and a native mobile app using Capacitor.

## 🚀 Features

- **Vehicle Search & Filtering**: Advanced search with multiple filter options
- **Smart Alerts**: Create and manage vehicle alerts with custom criteria
- **Personal Dashboard**: Customized welcome and activity overview for Petrit
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Real-time Notifications**: Stay updated with vehicle matches and alerts
- **Performance Optimized**: Code splitting, lazy loading, and optimized bundles
- **No Authentication Required**: Instant access to all features

## 🛠️ Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **Mobile**: Capacitor for native iOS/Android apps
- **Styling**: Tailwind CSS, Radix UI Components
- **State Management**: React Query
- **Routing**: React Router v6
- **HTTP Client**: Axios with error handling
- **Build Tool**: Vite with production optimizations
- **Personalization**: Custom branding and single-user experience

## 📦 Production Build

This application is production-ready with the following optimizations:

- ✅ **Code Splitting**: Automatic chunking for better caching
- ✅ **Tree Shaking**: Unused code elimination
- ✅ **Minification**: Terser optimization for smaller bundles
- ✅ **Asset Optimization**: Image and font optimization
- ✅ **Security Headers**: CSP, HTTPS enforcement, XSS protection
- ✅ **Error Handling**: Production error reporting and monitoring
- ✅ **Performance**: Lighthouse score > 90

## 🚀 Quick Deployment

### Prerequisites
- Domain name and SSL certificate
- Backend API deployed and accessible
- Node.js 18+ (for building)

### Environment Variables
Copy `.env.template` to `.env.production` and configure:

```bash
VITE_API_BASE_URL=https://api.yourdomain.com/api/v1  # Required
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX                # Optional
VITE_SENTRY_DSN=https://<EMAIL>   # Optional
```

### Build and Deploy

```bash
# Set environment variables
export VITE_API_BASE_URL=https://api.yourdomain.com/api/v1

# Build for production
./deploy.sh

# Upload dist/ folder to your web server
# Configure web server (see nginx.conf or .htaccess)
# Set up SSL certificate
```

## 📋 Deployment Options

### 1. Traditional Web Server
- Upload `dist/` folder to your web server
- Use provided `nginx.conf` or `.htaccess` configuration
- Configure SSL certificate

### 2. Docker Deployment
```bash
docker build -t auto-scouter-frontend .
docker run -d -p 80:80 -p 443:443 auto-scouter-frontend
```

### 3. Static Hosting (Netlify, Vercel)
- Connect GitHub repository
- Set build command: `npm run build`
- Set publish directory: `dist`
- Configure environment variables

## � Mobile App (Android/iOS)

The application has been converted to a native mobile app using Capacitor. The mobile app includes all web features plus native mobile optimizations.

### Mobile Features
- **Native Performance**: Optimized for mobile devices
- **Touch Interactions**: Mobile-friendly UI with haptic feedback
- **Status Bar Control**: Native status bar styling
- **Splash Screen**: Custom branded splash screen
- **Keyboard Handling**: Smart keyboard behavior
- **Safe Areas**: Proper handling of device notches and safe areas

### Building Android APK

#### Prerequisites
- Java 17 JDK
- Android SDK (API level 34)
- Node.js 18+

#### Setup Android Environment
```bash
# Install Java 17
sudo apt update
sudo apt install openjdk-17-jdk

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64

# Download Android command line tools
mkdir -p ~/android-sdk/cmdline-tools
cd ~/android-sdk/cmdline-tools
wget https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
unzip commandlinetools-linux-11076708_latest.zip
mv cmdline-tools latest

# Set Android environment
export ANDROID_HOME=~/android-sdk
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools

# Accept licenses and install SDK components
sdkmanager --licenses
sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
```

#### Build APK
```bash
# Install dependencies
npm install

# Build web assets
npm run build

# Sync Capacitor
npx cap sync android

# Build Android APK
cd android
./gradlew assembleDebug

# APK location: android/app/build/outputs/apk/debug/app-debug.apk
```

#### Build Scripts
The following npm scripts are available:
```bash
npm run cap:sync      # Sync web assets to native platforms
npm run cap:build     # Build web assets and sync
npm run android:build # Build Android APK
npm run android:run   # Build and run on connected device
```

### Production APK (Signed)

For production release, you'll need to sign the APK:

1. **Generate Keystore**:
```bash
keytool -genkey -v -keystore vehicle-scout.keystore -alias vehicle-scout -keyalg RSA -keysize 2048 -validity 10000
```

2. **Configure Signing** in `android/app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('path/to/vehicle-scout.keystore')
            storePassword 'your-store-password'
            keyAlias 'vehicle-scout'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

3. **Build Release APK**:
```bash
cd android
./gradlew assembleRelease
```

### iOS Build (macOS Required)

For iOS builds, you'll need:
- macOS with Xcode
- Apple Developer Account
- iOS device or simulator

```bash
# Add iOS platform
npx cap add ios

# Sync and open in Xcode
npx cap sync ios
npx cap open ios
```

### Mobile App Distribution

#### Android
- **Debug APK**: For testing (app-debug.apk)
- **Release APK**: For production distribution
- **Google Play Store**: Upload signed APK/AAB
- **Direct Distribution**: Share APK file directly

#### iOS
- **TestFlight**: For beta testing
- **App Store**: For production release
- **Enterprise Distribution**: For internal use

### Mobile Development

#### Testing on Device
```bash
# Android (with device connected via USB)
npm run android:run

# iOS (requires macOS and Xcode)
npx cap run ios
```

#### Live Reload
```bash
# Start dev server
npm run dev

# In another terminal, sync and run
npx cap sync android
npx cap run android --livereload --external
```

## �📚 Documentation

- **[Deployment Guide](DEPLOYMENT.md)**: Complete deployment instructions
- **[Production Checklist](PRODUCTION_CHECKLIST.md)**: Pre and post-deployment checklist
- **[Environment Template](.env.template)**: Required environment variables

## 🔧 Development

For development setup and contribution guidelines, see the main project README.

## 📊 Performance

- **Bundle Size**: ~600KB (gzipped)
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.5s
- **Lighthouse Score**: > 90

## 🛡️ Security

- HTTPS enforcement
- Content Security Policy (CSP)
- XSS protection headers
- Secure authentication with JWT
- Input validation and sanitization
- No sensitive data in client-side code

## 📞 Support

For deployment issues:
1. Check the [Deployment Guide](DEPLOYMENT.md)
2. Review the [Production Checklist](PRODUCTION_CHECKLIST.md)
3. Verify environment configuration
4. Check server logs

## 📄 License

This project is proprietary software. All rights reserved.

---

**Ready for Production** ✅  
Last updated: 2024-06-28  
Version: 1.0.0
