import{j as e}from"./ui-B-v3Zwus.js";import{r as s}from"./router-B-XVe37S.js";import{B as a,I as t,a as i,C as r,b as l,c,d as n,f as d}from"./index-BNatCL7N.js";import{V as m}from"./VehicleGrid-CmQ-CDxH.js";import{X as x,P as h,E as o,d as u,A as j,C as g,B as p,T as v,e as N,G as f,f as y,S as w,F as b}from"./icons-DaMsayX9.js";import{S as A}from"./switch-DppGFgFi.js";import{u as k}from"./query-NIfa3cXZ.js";import{a as T}from"./api-BK_ENMzF.js";import"./vendor-DEQ385Nk.js";const R=["BMW","Mercedes-Benz","Audi","Volkswagen","Porsche","Ford"],C=["Gasoline","Diesel","Electric","Hybrid","LPG"],S=["Manual","Automatic"],M=()=>{const[r,l]=s.useState({priceRange:[0,1e5],makes:[],yearRange:[2010,2024],fuelTypes:[],transmissions:[]}),c=r.makes.length>0||r.fuelTypes.length>0||r.transmissions.length>0||r.priceRange[0]>0||r.priceRange[1]<1e5||r.yearRange[0]>2010||r.yearRange[1]<2024;return e.jsxs("div",{className:"space-y-4 p-4 border rounded-lg bg-muted/50",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-medium",children:"Quick Filters"}),c&&e.jsxs(a,{variant:"ghost",size:"sm",onClick:()=>{l({priceRange:[0,1e5],makes:[],yearRange:[2010,2024],fuelTypes:[],transmissions:[]})},children:[e.jsx(x,{className:"mr-1 h-3 w-3"}),"Clear All"]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Price Range"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(t,{type:"number",placeholder:"Min",value:r.priceRange[0]||"",onChange:e=>l(s=>({...s,priceRange:[parseInt(e.target.value)||0,s.priceRange[1]]})),className:"w-24"}),e.jsx("span",{className:"text-muted-foreground",children:"to"}),e.jsx(t,{type:"number",placeholder:"Max",value:r.priceRange[1]||"",onChange:e=>l(s=>({...s,priceRange:[s.priceRange[0],parseInt(e.target.value)||1e5]})),className:"w-24"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"EUR"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Year Range"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(t,{type:"number",placeholder:"From",value:r.yearRange[0]||"",onChange:e=>l(s=>({...s,yearRange:[parseInt(e.target.value)||2010,s.yearRange[1]]})),className:"w-20"}),e.jsx("span",{className:"text-muted-foreground",children:"to"}),e.jsx(t,{type:"number",placeholder:"To",value:r.yearRange[1]||"",onChange:e=>l(s=>({...s,yearRange:[s.yearRange[0],parseInt(e.target.value)||2024]})),className:"w-20"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Popular Makes"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:R.map(s=>e.jsx(i,{variant:r.makes.includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>(e=>{l(s=>({...s,makes:s.makes.includes(e)?s.makes.filter(s=>s!==e):[...s.makes,e]}))})(s),children:s},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Fuel Type"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:C.map(s=>e.jsx(i,{variant:r.fuelTypes.includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>(e=>{l(s=>({...s,fuelTypes:s.fuelTypes.includes(e)?s.fuelTypes.filter(s=>s!==e):[...s.fuelTypes,e]}))})(s),children:s},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Transmission"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:S.map(s=>e.jsx(i,{variant:r.transmissions.includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>(e=>{l(s=>({...s,transmissions:s.transmissions.includes(e)?s.transmissions.filter(s=>s!==e):[...s.transmissions,e]}))})(s),children:s},s))})]}),e.jsx("div",{className:"pt-2",children:e.jsx(a,{className:"w-full",children:"Apply Filters"})})]})},D=[{id:"1",name:"BMW 3 Series",criteria:"Under €25,000, 2015+",matches:12,isActive:!0,lastTriggered:new Date(Date.now()-72e5)},{id:"2",name:"Audi A4 Diesel",criteria:"Diesel, Manual, €20-30k",matches:8,isActive:!0,lastTriggered:new Date(Date.now()-216e5)},{id:"3",name:"Mercedes C-Class",criteria:"Automatic, <80k km",matches:5,isActive:!1},{id:"4",name:"VW Golf GTI",criteria:"Manual, 2018+",matches:15,isActive:!0,lastTriggered:new Date(Date.now()-864e5)}],V=()=>{const s=D.filter(e=>e.isActive),t=D.reduce((e,s)=>e+s.matches,0);return e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(c,{className:"text-lg",children:"My Alerts"}),e.jsxs(a,{size:"sm",className:"auto-scouter-gradient",children:[e.jsx(h,{className:"mr-1 h-3 w-3"}),"New"]})]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:s.length}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Active Alerts"})]}),e.jsxs("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:t}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Total Matches"})]})]}),e.jsx("div",{className:"space-y-3",children:D.slice(0,4).map(s=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h4",{className:"font-medium text-sm truncate",children:s.name}),e.jsx(i,{variant:s.isActive?"success":"secondary",className:"text-xs",children:s.matches})]}),e.jsx("p",{className:"text-xs text-muted-foreground truncate",children:s.criteria}),s.lastTriggered&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Last: ",s.lastTriggered.toLocaleDateString()]})]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(A,{checked:s.isActive,onCheckedChange:()=>{s.id}})})]},s.id))}),e.jsxs(a,{variant:"outline",className:"w-full",children:[e.jsx(o,{className:"mr-2 h-4 w-4"}),"View All Alerts"]})]})]})};const F=new class{async getDashboardStats(){try{return(await T.get("/dashboard/stats")).data}catch(e){return{activeAlerts:0,newMatches:0,unreadNotifications:0,vehiclesViewed:0,recentActivity:[]}}}async getVehicleStats(){try{return(await T.get("/cars/stats")).data}catch(e){return{total:0,averagePrice:0,priceRange:{min:0,max:0},popularMakes:[]}}}},_=()=>k({queryKey:["dashboard","stats"],queryFn:()=>F.getDashboardStats(),staleTime:12e4,gcTime:3e5,refetchInterval:3e4}),G=()=>{const{data:s,isLoading:a,error:t}=_();return a?e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(c,{className:"text-lg",children:"Recent Activity"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:[1,2,3].map(s=>e.jsxs("div",{className:"flex items-start space-x-3 p-3",children:[e.jsx("div",{className:"w-8 h-8 bg-muted animate-pulse rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 w-32 bg-muted animate-pulse rounded mb-2"}),e.jsx("div",{className:"h-3 w-48 bg-muted animate-pulse rounded"})]})]},s))})})]}):t||!s?.recentActivity?e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(c,{className:"text-lg",children:"Recent Activity"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground text-center py-4",children:"No recent activity available"})})]}):e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(c,{className:"text-lg",children:"Recent Activity"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:s.recentActivity.map(s=>{const a=(e=>{switch(e){case"vehicle_match":return g;case"alert_created":case"alert_triggered":return j;case"vehicle_viewed":return o;default:return u}})(s.type),t=(e=>{switch(e){case"vehicle_match":return{text:"Match",variant:"success"};case"alert_created":return{text:"Alert",variant:"default"};case"vehicle_viewed":return{text:"Viewed",variant:"secondary"};case"alert_triggered":return{text:"Triggered",variant:"warning"};default:return{text:"Activity",variant:"secondary"}}})(s.type);return e.jsxs("div",{className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(a,{className:"h-4 w-4 text-primary"})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:s.title}),e.jsx(i,{variant:t.variant,className:"text-xs",children:t.text})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.description}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:d(new Date(s.timestamp))})]})]},s.id)})})})]})},I=()=>{const{data:s,isLoading:a,error:t}=_();if(a)return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(s=>e.jsxs(r,{className:"card-hover",children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx("div",{className:"h-4 w-20 bg-muted animate-pulse rounded"}),e.jsx("div",{className:"h-4 w-4 bg-muted animate-pulse rounded"})]}),e.jsxs(n,{children:[e.jsx("div",{className:"h-8 w-16 bg-muted animate-pulse rounded mb-2"}),e.jsx("div",{className:"h-3 w-24 bg-muted animate-pulse rounded"})]})]},s))});if(t||!s)return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.jsx(r,{className:"card-hover",children:e.jsx(n,{className:"p-6 text-center",children:e.jsx("p",{className:"text-red-600 text-sm",children:"Failed to load stats"})})})});const d=[{title:"Active Alerts",value:s.activeAlerts.toString(),change:"+2",changeType:"increase",icon:j,description:"Monitoring vehicle matches"},{title:"New Matches",value:s.newMatches.toString(),change:"+12",changeType:"increase",icon:g,description:"This week"},{title:"Notifications",value:s.unreadNotifications.toString(),change:"-5",changeType:"decrease",icon:p,description:"Unread notifications"},{title:"Vehicles Viewed",value:s.vehiclesViewed.toString(),change:"+28",changeType:"increase",icon:o,description:"Last 30 days"}];return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:d.map(s=>{const a=s.icon;return e.jsxs(r,{className:"card-hover",children:[e.jsxs(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium text-muted-foreground",children:s.title}),e.jsx(a,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(n,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:s.value}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.description})]}),e.jsxs(i,{variant:"increase"===s.changeType?"success":"warning",className:"flex items-center space-x-1",children:["increase"===s.changeType?e.jsx(v,{className:"h-3 w-3"}):e.jsx(N,{className:"h-3 w-3"}),e.jsx("span",{children:s.change})]})]})})]},s.title)})})},L=()=>{const[i,d]=s.useState(""),[x,o]=s.useState("grid"),[u,j]=s.useState(!1);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("header",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold tracking-tight",children:"Welcome back, Petrit!"}),e.jsx("p",{className:"text-muted-foreground text-sm sm:text-base",children:"Here's what's happening with your vehicle search and alerts."})]}),e.jsxs(a,{className:"auto-scouter-gradient w-full sm:w-auto","aria-label":"Create a new vehicle alert",children:[e.jsx(h,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),e.jsx("span",{className:"sm:inline",children:"Create Alert"})]})]}),e.jsx(I,{}),e.jsxs("main",{className:"grid grid-cols-1 xl:grid-cols-3 gap-6",children:[e.jsx("section",{className:"xl:col-span-2 space-y-6","aria-labelledby":"vehicle-matches-heading",children:e.jsxs(r,{children:[e.jsx(l,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(c,{id:"vehicle-matches-heading",className:"text-lg",children:"Recent Vehicle Matches"}),e.jsxs("div",{className:"flex items-center space-x-2",role:"group","aria-label":"View mode selection",children:[e.jsx(a,{variant:"grid"===x?"default":"outline",size:"sm",onClick:()=>o("grid"),"aria-label":"Grid view","aria-pressed":"grid"===x,children:e.jsx(f,{className:"h-4 w-4","aria-hidden":"true"})}),e.jsx(a,{variant:"list"===x?"default":"outline",size:"sm",onClick:()=>o("list"),"aria-label":"List view","aria-pressed":"list"===x,children:e.jsx(y,{className:"h-4 w-4","aria-hidden":"true"})})]})]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","aria-hidden":"true"}),e.jsx(t,{placeholder:"Search vehicles...",value:i,onChange:e=>d(e.target.value),className:"pl-10","aria-label":"Search vehicles by make, model, or other criteria"})]}),e.jsxs(a,{variant:"outline",onClick:()=>j(!u),className:"w-full sm:w-auto","aria-expanded":u,"aria-controls":"vehicle-filters","aria-label":u?"Hide filters":"Show filters",children:[e.jsx(b,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),e.jsx("span",{children:"Filters"})]})]}),u&&e.jsx("div",{id:"vehicle-filters",children:e.jsx(M,{})}),e.jsx(m,{viewMode:x,searchTerm:i})]})]})}),e.jsxs("aside",{className:"space-y-6","aria-label":"Dashboard sidebar",children:[e.jsx(V,{}),e.jsx(G,{}),e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(c,{className:"text-lg",children:"Quick Actions"})}),e.jsxs(n,{className:"space-y-3",children:[e.jsxs(a,{variant:"outline",className:"w-full justify-start","aria-label":"Create a new vehicle alert",children:[e.jsx(h,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),"Create New Alert"]}),e.jsxs(a,{variant:"outline",className:"w-full justify-start","aria-label":"Open advanced vehicle search",children:[e.jsx(w,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),"Advanced Search"]}),e.jsxs(a,{variant:"outline",className:"w-full justify-start","aria-label":"View saved vehicle searches",children:[e.jsx(b,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),"Saved Searches"]})]})]})]})]})]})};export{L as Dashboard};
