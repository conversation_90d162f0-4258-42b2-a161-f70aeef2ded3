import{j as e}from"./ui-B-v3Zwus.js";import{r as s,b as a,L as t}from"./router-B-XVe37S.js";import{C as r,d as i,a as c,e as l,g as n,B as d}from"./index-BNatCL7N.js";import{u as m}from"./query-NIfa3cXZ.js";import{a as o}from"./api-BK_ENMzF.js";import{g as h,h as x,i as u,j as p,p as j,q as y}from"./icons-DaMsayX9.js";const g=new class{async searchVehicles(e={}){try{return(await o.get("/cars",{params:e})).data}catch(s){throw s}}async getVehicle(e){try{return(await o.get(`/cars/${e}`)).data}catch(s){throw s}}async getRecentVehicles(e=20){try{return(await o.get("/cars",{params:{limit:e,sort:"newest"}})).data.vehicles||[]}catch(s){return[]}}async getFeaturedVehicles(){try{return(await o.get("/cars",{params:{limit:6,sort:"price_asc"}})).data.vehicles||[]}catch(e){return[]}}async getVehiclesByMake(e){try{return(await o.get("/cars",{params:{make:e,limit:50}})).data.vehicles||[]}catch(s){return[]}}async getPopularMakes(){try{return["Volkswagen","Peugeot","Citroën","Opel","BMW","Audi","Mercedes-Benz","Fiat","Ford","Renault","Toyota","Honda","Jeep","Mini"]}catch(e){return[]}}async getModelsByMake(e){try{const s=await this.getVehiclesByMake(e);return[...new Set(s.map(e=>e.model).filter(Boolean))].sort()}catch(s){return[]}}async triggerScrape(){try{await o.post("/scrape")}catch(e){throw e}}async getVehicleStats(){try{return(await o.get("/cars/stats")).data}catch(e){return{total:0,averagePrice:0,priceRange:{min:0,max:0},popularMakes:[]}}}async getSavedVehicles(){try{return(await o.get("/cars/saved")).data.vehicles||[]}catch(e){return[]}}async getSimilarVehicles(e,s=5){try{return(await o.get(`/cars/${e}/similar`,{params:{limit:s}})).data.vehicles||[]}catch(a){return[]}}async getRecommendations(e){try{return(await o.get("/cars/recommendations",{params:e?{userId:e}:{}})).data.vehicles||[]}catch(s){return[]}}async getFilterOptions(){try{return(await o.get("/cars/filter-options")).data}catch(e){return{makes:await this.getPopularMakes(),fuelTypes:["Gasoline","Diesel","Electric","Hybrid"],transmissions:["Manual","Automatic"],bodyTypes:["Sedan","SUV","Hatchback","Coupe","Wagon"]}}}async saveVehicle(e){try{await o.post(`/cars/${e}/save`)}catch(s){throw s}}async unsaveVehicle(e){try{await o.delete(`/cars/${e}/save`)}catch(s){throw s}}async reportVehicle(e,s,a){try{await o.post(`/cars/${e}/report`,{reason:s,description:a})}catch(t){throw t}}async advancedSearch(e){try{return(await o.post("/cars/search",e)).data}catch(s){throw s}}async exportSearchResults(e,s="csv"){try{return(await o.post("/cars/export",{...e,format:s},{responseType:"blob"})).data}catch(a){throw a}}async getSystemStats(){try{return(await o.get("/stats")).data}catch(e){throw e}}},f={all:["vehicles"],lists:()=>[...f.all,"list"],list:e=>[...f.lists(),e],details:()=>[...f.all,"detail"],detail:e=>[...f.details(),e],recent:()=>[...f.all,"recent"],featured:()=>[...f.all,"featured"],saved:()=>[...f.all,"saved"],stats:()=>[...f.all,"stats"],recommendations:()=>[...f.all,"recommendations"],similar:e=>[...f.all,"similar",e],filterOptions:()=>[...f.all,"filter-options"]},v=(e={})=>m({queryKey:f.list(e),queryFn:()=>g.searchVehicles(e),staleTime:3e5,gcTime:6e5}),w=({viewMode:m,searchTerm:o,filters:g})=>{const[f,w]=s.useState(new Set),{data:N,isLoading:b,error:k}=v({make:g?.make,model:g?.model,priceMin:g?.minPrice,priceMax:g?.maxPrice,yearMin:g?.minYear,yearMax:g?.maxYear,maxMileage:g?.maxMileage,fuelType:g?.fuelType?.[0],transmission:g?.transmission?.[0],bodyType:g?.bodyType?.[0],limit:20}),V=a.useMemo(()=>N?.vehicles?o?N.vehicles.filter(e=>`${e.make} ${e.model}`.toLowerCase().includes(o.toLowerCase())||e.location.toLowerCase().includes(o.toLowerCase())):N.vehicles:[],[N?.vehicles,o]),M=e=>{w(s=>{const a=new Set(s);return a.has(e)?a.delete(e):a.add(e),a})};return b?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-muted-foreground",children:"Loading vehicles..."})]})}):k?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-red-600 mb-2",children:"Failed to load vehicles"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Please try again later"})]})}):V.length?"list"===m?e.jsx("div",{className:"space-y-4",role:"list","aria-label":"Vehicle listings",children:V.map(s=>e.jsx(r,{className:"card-hover",role:"listitem",children:e.jsx(i,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-16 bg-muted rounded-lg flex items-center justify-center","aria-hidden":"true",children:e.jsx("span",{className:"text-xs text-muted-foreground",children:"No Image"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("h3",{className:"font-semibold",children:[s.make," ",s.model]}),s.scrapedAt&&new Date(s.scrapedAt)>new Date(Date.now()-864e5)&&e.jsx(c,{variant:"success",children:"New"})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(h,{className:"mr-1 h-3 w-3","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Year: "}),s.year]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(x,{className:"mr-1 h-3 w-3","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Mileage: "}),l(s.mileage||0)," km"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(u,{className:"mr-1 h-3 w-3","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Fuel type: "}),s.fuelType]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(p,{className:"mr-1 h-3 w-3","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Location: "}),s.location]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-2xl font-bold",children:n(s.price||0)}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s.transmission})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(d,{variant:"ghost",size:"icon",onClick:()=>M(s.id),"aria-label":f.has(s.id)?`Remove ${s.make} ${s.model} from favorites`:`Add ${s.make} ${s.model} to favorites`,children:e.jsx(j,{className:"h-4 w-4 "+(f.has(s.id)?"fill-red-500 text-red-500":""),"aria-hidden":"true"})}),e.jsx(t,{to:`/vehicle/${s.id}`,children:e.jsxs(d,{variant:"outline",size:"sm","aria-label":`View details for ${s.make} ${s.model}`,children:[e.jsx(y,{className:"mr-2 h-4 w-4","aria-hidden":"true"}),"View"]})})]})]})]})})},s.id))}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",role:"grid","aria-label":"Vehicle listings",children:V.map(s=>e.jsx(r,{className:"card-hover",role:"gridcell",children:e.jsx(i,{className:"p-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"w-full h-48 bg-muted rounded-lg flex items-center justify-center","aria-hidden":"true",children:e.jsx("span",{className:"text-muted-foreground",children:"No Image Available"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"font-semibold text-lg",children:[s.make," ",s.model]}),e.jsx(d,{variant:"ghost",size:"icon",onClick:()=>M(s.id),children:e.jsx(j,{className:"h-4 w-4 "+(f.has(s.id)?"fill-red-500 text-red-500":"")})})]}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx(c,{variant:"outline",children:s.year}),e.jsx(c,{variant:"outline",children:s.transmission}),s.scrapedAt&&new Date(s.scrapedAt)>new Date(Date.now()-864e5)&&e.jsx(c,{variant:"success",children:"New"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mt-3 text-sm text-muted-foreground",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(x,{className:"mr-1 h-3 w-3"}),l(s.mileage||0)," km"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(u,{className:"mr-1 h-3 w-3"}),s.fuelType]}),e.jsxs("span",{className:"flex items-center col-span-2",children:[e.jsx(p,{className:"mr-1 h-3 w-3"}),s.location]})]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-2 border-t",children:[e.jsx("div",{className:"text-2xl font-bold",children:n(s.price||0)}),e.jsx(t,{to:`/vehicle/${s.id}`,children:e.jsxs(d,{variant:"outline",size:"sm",children:[e.jsx(y,{className:"mr-2 h-4 w-4"}),"View Details"]})})]})]})})},s.id))}):e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2",children:"No vehicles found"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Try adjusting your search criteria"})]})})};export{w as V,v as u};
