import{j as s}from"./ui-B-v3Zwus.js";import{r as e}from"./router-B-XVe37S.js";import{B as a,C as l,d as i,b as r,c}from"./index-BNatCL7N.js";import{T as n,a as t,b as m,c as d}from"./tabs-CVrXG0tV.js";import{r as o,p as x,s as h,g as j,h as u,i as N,c as p,j as v,t as f,u as g,q as w}from"./icons-DaMsayX9.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";const y={make:"BMW",model:"3 Series",year:2022,price:35e3,mileage:15e3,fuelType:"Gasoline",transmission:"Automatic",bodyType:"Sedan",color:"Alpine White",location:"San Francisco, CA",dealer:"BMW of San Francisco",description:"Excellent condition BMW 3 Series with premium package. One owner, clean title, full service history.",features:["Premium Package","Navigation System","Leather Seats","Sunroof","Heated Seats","Backup Camera"],images:["/api/placeholder/800/600","/api/placeholder/800/600","/api/placeholder/800/600"],vin:"WBA8E9G50HNU12345",engineSize:"2.0L Turbo",doors:4,isNew:!1,contactPhone:"(*************"},S=({vehicleId:S,onBack:b})=>{const[k,C]=e.useState(0),[T,B]=e.useState(!1),A=y;return s.jsxs("div",{className:"max-w-7xl mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(a,{variant:"ghost",onClick:b,className:"flex items-center",children:[s.jsx(o,{className:"h-4 w-4 mr-2"}),"Back to Search"]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs(a,{variant:"outline",size:"sm",onClick:()=>B(!T),children:[s.jsx(x,{className:"h-4 w-4 mr-2 "+(T?"fill-red-500 text-red-500":"")}),T?"Saved":"Save"]}),s.jsxs(a,{variant:"outline",size:"sm",children:[s.jsx(h,{className:"h-4 w-4 mr-2"}),"Share"]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsx(l,{children:s.jsxs(i,{className:"p-0",children:[s.jsxs("div",{className:"relative",children:[s.jsx("img",{src:A.images[k],alt:`${A.make} ${A.model}`,className:"w-full h-96 object-cover rounded-t-lg"}),A.isNew]}),A.images.length>1&&s.jsx("div",{className:"flex space-x-2 p-4",children:A.images.map((e,a)=>s.jsx("button",{onClick:()=>C(a),className:"w-20 h-16 rounded border-2 overflow-hidden "+(a===k?"border-blue-500":"border-gray-200"),children:s.jsx("img",{src:e,alt:`View ${a+1}`,className:"w-full h-full object-cover"})},a))})]})}),s.jsx(l,{children:s.jsxs(n,{defaultValue:"overview",className:"w-full",children:[s.jsxs(t,{className:"grid w-full grid-cols-3",children:[s.jsx(m,{value:"overview",children:"Overview"}),s.jsx(m,{value:"features",children:"Features"}),s.jsx(m,{value:"history",children:"History"})]}),s.jsxs(d,{value:"overview",className:"p-6",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(j,{className:"h-4 w-4 mr-2 text-muted-foreground"}),s.jsxs("span",{className:"text-sm",children:["Year: ",A.year]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx(u,{className:"h-4 w-4 mr-2 text-muted-foreground"}),s.jsxs("span",{className:"text-sm",children:["Mileage: ",(D=A.mileage,new Intl.NumberFormat("en-US").format(D))," miles"]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx(N,{className:"h-4 w-4 mr-2 text-muted-foreground"}),s.jsxs("span",{className:"text-sm",children:["Fuel: ",A.fuelType]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx(p,{className:"h-4 w-4 mr-2 text-muted-foreground"}),s.jsxs("span",{className:"text-sm",children:["Transmission: ",A.transmission]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Body Type:"})," ",A.bodyType]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Color:"})," ",A.color]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Engine:"})," ",A.engineSize]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Doors:"})," ",A.doors]})]})]}),s.jsxs("div",{className:"mt-6",children:[s.jsx("h4",{className:"font-medium mb-2",children:"Description"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:A.description})]})]}),s.jsx(d,{value:"features",className:"p-6",children:s.jsx("div",{className:"grid grid-cols-2 gap-2",children:A.features.map((e,a)=>s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),s.jsx("span",{className:"text-sm",children:e})]},a))})}),s.jsx(d,{value:"history",className:"p-6",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"VIN:"})," ",A.vin]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Title:"})," Clean"]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Accidents:"})," None reported"]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Service Records:"})," Available"]})]})})]})})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(l,{children:[s.jsxs(r,{children:[s.jsx(c,{className:"text-2xl font-bold",children:(F=A.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(F))}),s.jsxs("div",{className:"flex items-center text-muted-foreground",children:[s.jsx(v,{className:"h-4 w-4 mr-1"}),s.jsx("span",{className:"text-sm",children:A.location})]})]}),s.jsxs(i,{className:"space-y-4",children:[s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Dealer:"})," ",A.dealer]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs(a,{variant:"outline",className:"w-full justify-start",children:[s.jsx(f,{className:"h-4 w-4 mr-2"}),A.contactPhone]}),s.jsxs(a,{variant:"outline",className:"w-full justify-start",children:[s.jsx(g,{className:"h-4 w-4 mr-2"}),"Contact Dealer"]}),s.jsxs(a,{className:"w-full",children:[s.jsx(w,{className:"h-4 w-4 mr-2"}),"View Original Listing"]})]})]})]}),s.jsxs(l,{children:[s.jsx(r,{children:s.jsx(c,{className:"text-lg",children:"Quick Actions"})}),s.jsxs(i,{className:"space-y-3",children:[s.jsx(a,{variant:"outline",className:"w-full justify-start",children:"Create Alert for Similar"}),s.jsx(a,{variant:"outline",className:"w-full justify-start",children:"Schedule Test Drive"}),s.jsx(a,{variant:"outline",className:"w-full justify-start",children:"Get Financing Quote"})]})]})]})]})]});var F,D};export{S as VehicleDetail};
