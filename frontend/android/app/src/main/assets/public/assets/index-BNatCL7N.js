const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-DNn_tAV6.js","assets/ui-B-v3Zwus.js","assets/router-B-XVe37S.js","assets/vendor-DEQ385Nk.js","assets/query-NIfa3cXZ.js","assets/icons-DaMsayX9.js","assets/web-BMusv0SZ.js","assets/web-BfGi1d1w.js","assets/Dashboard-DW2SyjMw.js","assets/VehicleGrid-CmQ-CDxH.js","assets/api-BK_ENMzF.js","assets/switch-DppGFgFi.js","assets/VehicleSearch-DwNl0vTT.js","assets/tabs-CVrXG0tV.js","assets/dialog-D3B6nCUe.js","assets/VehicleDetail-DOj6uqiX.js","assets/AlertManager-DIHB6BvX.js","assets/dropdown-menu-Dvsgogy5.js","assets/NotificationCenter-DvqP_5ur.js"])))=>i.map(i=>d[i]);
import{j as e,S as t}from"./ui-B-v3Zwus.js";import{r,N as o,L as n,b as s,B as a,d as i,e as l,f as c}from"./router-B-XVe37S.js";import{a as d,g as u}from"./vendor-DEQ385Nk.js";import{Q as p,a as m}from"./query-NIfa3cXZ.js";import{A as f,R as h,H as b,C as g,L as v,a as x,S as y,B as w,M as j,X as N,b as k,c as E}from"./icons-DaMsayX9.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var P,A={};const S=u(function(){if(P)return A;P=1;var e=d();return A.createRoot=e.createRoot,A.hydrateRoot=e.hydrateRoot,A}()),C={},L=function(e,t,r){let o=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const r=document.querySelector("meta[property=csp-nonce]"),n=r?.nonce||r?.getAttribute("nonce");o=e(t.map(e=>{if((e=function(e){return"/"+e}(e))in C)return;C[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script"),o.crossOrigin="",o.href=e,n&&o.setAttribute("nonce",n),document.head.appendChild(o),t?new Promise((t,r)=>{o.addEventListener("load",t),o.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})},I={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"https://api.autoscouter.com/api/v1",VITE_API_TIMEOUT:"10000",VITE_APP_ENVIRONMENT:"production",VITE_APP_NAME:"Auto Scouter",VITE_APP_VERSION:"1.0.0",VITE_BUILD_TIMESTAMP:"",VITE_CACHE_VERSION:"1.0.0",VITE_ENABLE_ANALYTICS:"true",VITE_ENABLE_ERROR_REPORTING:"true",VITE_ENABLE_HTTPS_ONLY:"true",VITE_ENABLE_PERFORMANCE_MONITORING:"true",VITE_ENABLE_STRICT_CSP:"true",VITE_GIT_COMMIT:"",VITE_GOOGLE_ANALYTICS_ID:"G-XXXXXXXXXX",VITE_SENTRY_DSN:"https://<EMAIL>/project-id"},_="1.0.0",O="production";(()=>{const e=["VITE_API_BASE_URL"].filter(e=>!I[e]);if(e.length>0)throw new Error(`Missing required environment variables: ${e.join(", ")}`)})();class T{static instance;isProduction=!0;sentryDsn="https://<EMAIL>/project-id";constructor(){this.setupGlobalErrorHandlers()}static getInstance(){return T.instance||(T.instance=new T),T.instance}setupGlobalErrorHandlers(){window.addEventListener("unhandledrejection",e=>{this.logError(new Error(e.reason),{type:"unhandledrejection",reason:e.reason})}),window.addEventListener("error",e=>{this.logError(new Error(e.message),{type:"javascript",filename:e.filename,lineno:e.lineno,colno:e.colno})})}logError(e,t){const r={message:e.message,stack:e.stack,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href,...t};this.isProduction&&this.sendToErrorService(r)}async sendToErrorService(e){try{this.sentryDsn&&await fetch("/api/errors",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(t){}}logUserAction(e,t){this.isProduction&&this.sendAnalyticsEvent("user_action",{action:e,...t,timestamp:(new Date).toISOString()})}async sendAnalyticsEvent(e,t){try{"G-XXXXXXXXXX"}catch(r){}}}const R=T.getInstance(),z=(e=>e.CapacitorPlatforms=(e=>{const t=new Map;t.set("web",{name:"web"});const r=e.CapacitorPlatforms||{currentPlatform:{name:"web"},platforms:t};return r.addPlatform=(e,t)=>{r.platforms.set(e,t)},r.setPlatform=e=>{r.platforms.has(e)&&(r.currentPlatform=r.platforms.get(e))},r})(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});
/*! Capacitor: https://capacitorjs.com/ - MIT License */var D,M;z.addPlatform,z.setPlatform,(M=D||(D={})).Unimplemented="UNIMPLEMENTED",M.Unavailable="UNAVAILABLE";class $ extends Error{constructor(e,t,r){super(e),this.message=e,this.code=t,this.data=r}}const V=e=>{var t,r,o,n,s;const a=e.CapacitorCustomPlatform||null,i=e.Capacitor||{},l=i.Plugins=i.Plugins||{},c=e.CapacitorPlatforms,d=(null===(t=null==c?void 0:c.currentPlatform)||void 0===t?void 0:t.getPlatform)||(()=>null!==a?a.name:(e=>{var t,r;return(null==e?void 0:e.androidBridge)?"android":(null===(r=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===r?void 0:r.bridge)?"ios":"web"})(e)),u=(null===(r=null==c?void 0:c.currentPlatform)||void 0===r?void 0:r.isNativePlatform)||(()=>"web"!==d()),p=(null===(o=null==c?void 0:c.currentPlatform)||void 0===o?void 0:o.isPluginAvailable)||(e=>{const t=f.get(e);return!!(null==t?void 0:t.platforms.has(d()))||!!m(e)}),m=(null===(n=null==c?void 0:c.currentPlatform)||void 0===n?void 0:n.getPluginHeader)||(e=>{var t;return null===(t=i.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)}),f=new Map,h=(null===(s=null==c?void 0:c.currentPlatform)||void 0===s?void 0:s.registerPlugin)||((e,t={})=>{const r=f.get(e);if(r)return r.proxy;const o=d(),n=m(e);let s;const c=r=>{let l;const c=(...c)=>{const d=(async()=>(!s&&o in t?s=s="function"==typeof t[o]?await t[o]():t[o]:null!==a&&!s&&"web"in t&&(s=s="function"==typeof t.web?await t.web():t.web),s))().then(t=>{const s=((t,r)=>{var s,a;if(!n){if(t)return null===(a=t[r])||void 0===a?void 0:a.bind(t);throw new $(`"${e}" plugin is not implemented on ${o}`,D.Unimplemented)}{const o=null==n?void 0:n.methods.find(e=>r===e.name);if(o)return"promise"===o.rtype?t=>i.nativePromise(e,r.toString(),t):(t,o)=>i.nativeCallback(e,r.toString(),t,o);if(t)return null===(s=t[r])||void 0===s?void 0:s.bind(t)}})(t,r);if(s){const e=s(...c);return l=null==e?void 0:e.remove,e}throw new $(`"${e}.${r}()" is not implemented on ${o}`,D.Unimplemented)});return"addListener"===r&&(d.remove=async()=>l()),d};return c.toString=()=>`${r.toString()}() { [capacitor code] }`,Object.defineProperty(c,"name",{value:r,writable:!1,configurable:!1}),c},u=c("addListener"),p=c("removeListener"),h=(e,t)=>{const r=u({eventName:e},t),o=async()=>{const o=await r;p({eventName:e,callbackId:o},t)},n=new Promise(e=>r.then(()=>e({remove:o})));return n.remove=async()=>{await o()},n},b=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return n?h:u;case"removeListener":return p;default:return c(t)}}});return l[e]=b,f.set(e,{name:e,proxy:b,platforms:new Set([...Object.keys(t),...n?[o]:[]])}),b});return i.convertFileSrc||(i.convertFileSrc=e=>e),i.getPlatform=d,i.handleError=t=>e.console.error(t),i.isNativePlatform=u,i.isPluginAvailable=p,i.pluginMethodNoop=(e,t,r)=>Promise.reject(`${r} does not have an implementation of "${t}".`),i.registerPlugin=h,i.Exception=$,i.DEBUG=!!i.DEBUG,i.isLoggingEnabled=!!i.isLoggingEnabled,i.platform=i.getPlatform(),i.isNative=i.isNativePlatform(),i},G=(e=>e.Capacitor=V(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),B=G.registerPlugin;G.Plugins;class U{constructor(e){this.listeners={},this.retainedEventArguments={},this.windowListeners={},e&&(this.config=e)}addListener(e,t){let r=!1;this.listeners[e]||(this.listeners[e]=[],r=!0),this.listeners[e].push(t);const o=this.windowListeners[e];o&&!o.registered&&this.addWindowListener(o),r&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,r){const o=this.listeners[e];if(o)o.forEach(e=>e(t));else if(r){let r=this.retainedEventArguments[e];r||(r=[]),r.push(t),this.retainedEventArguments[e]=r}}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new G.Exception(e,D.Unimplemented)}unavailable(e="not available"){return new G.Exception(e,D.Unavailable)}async removeListener(e,t){const r=this.listeners[e];if(!r)return;const o=r.indexOf(t);this.listeners[e].splice(o,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const H=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),W=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class F extends U{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[r,o]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");r=W(r).trim(),o=W(o).trim(),t[r]=o}),t}async setCookie(e){try{const t=H(e.key),r=H(e.value),o=`; expires=${(e.expires||"").replace("expires=","")}`,n=(e.path||"/").replace("path=",""),s=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${r||""}${o}; path=${n}; ${s};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}B("CapacitorCookies",{web:()=>new F});const X=(e,t={})=>{const r=Object.assign({method:e.method||"GET",headers:e.headers},t),o=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((r,o,n)=>(r[o]=e[t[n]],r),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)r.body=e.data;else if(o.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[r,o]of Object.entries(e.data||{}))t.set(r,o);r.body=t.toString()}else if(o.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,r)=>{t.append(r,e)});else for(const r of Object.keys(e.data))t.append(r,e.data[r]);r.body=t;const o=new Headers(r.headers);o.delete("content-type"),r.headers=o}else(o.includes("application/json")||"object"==typeof e.data)&&(r.body=JSON.stringify(e.data));return r};class q extends U{async request(e){const t=X(e,e.webFetchExtra),r=((e,t=!0)=>e?Object.entries(e).reduce((e,r)=>{const[o,n]=r;let s,a;return Array.isArray(n)?(a="",n.forEach(e=>{s=t?encodeURIComponent(e):e,a+=`${o}=${s}&`}),a.slice(0,-1)):(s=t?encodeURIComponent(n):n,a=`${o}=${s}`),`${e}&${a}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),o=r?`${e.url}?${r}`:e.url,n=await fetch(o,t),s=n.headers.get("content-type")||"";let a,i,{responseType:l="text"}=n.ok?e:{};switch(s.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":i=await n.blob(),a=await(async e=>new Promise((t,r)=>{const o=new FileReader;o.onload=()=>{const e=o.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},o.onerror=e=>r(e),o.readAsDataURL(e)}))(i);break;case"json":a=await n.json();break;default:a=await n.text()}const c={};return n.headers.forEach((e,t)=>{c[t]=e}),{data:a,headers:c,status:n.status,url:n.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}var K,Y,J,Q;B("CapacitorHttp",{web:()=>new q}),(Y=K||(K={})).Dark="DARK",Y.Light="LIGHT",Y.Default="DEFAULT",(Q=J||(J={})).None="NONE",Q.Slide="SLIDE",Q.Fade="FADE";const Z=B("StatusBar"),ee=B("SplashScreen",{web:()=>L(()=>import("./web-DNn_tAV6.js"),__vite__mapDeps([0,1,2,3,4,5])).then(e=>new e.SplashScreenWeb)});var te,re,oe,ne;(re=te||(te={})).Dark="DARK",re.Light="LIGHT",re.Default="DEFAULT",(ne=oe||(oe={})).Body="body",ne.Ionic="ionic",ne.Native="native",ne.None="none";const se=B("Keyboard");var ae,ie,le,ce;(ie=ae||(ae={})).Heavy="HEAVY",ie.Medium="MEDIUM",ie.Light="LIGHT",(ce=le||(le={})).Success="SUCCESS",ce.Warning="WARNING",ce.Error="ERROR";const de=B("Haptics",{web:()=>L(()=>import("./web-BMusv0SZ.js"),__vite__mapDeps([6,1,2,3,4,5])).then(e=>new e.HapticsWeb)});class ue{static isNative(){return G.isNativePlatform()}static isAndroid(){return"android"===G.getPlatform()}static isIOS(){return"ios"===G.getPlatform()}static async initializeApp(){if(this.isNative())try{await Z.setStyle({style:K.Dark}),await Z.setBackgroundColor({color:"#000000"}),await ee.hide(),this.isAndroid()&&(se.addListener("keyboardWillShow",()=>{document.body.classList.add("keyboard-open")}),se.addListener("keyboardWillHide",()=>{document.body.classList.remove("keyboard-open")}))}catch(e){}}static async provideFeedback(e="light"){if(this.isNative())try{const t="light"===e?ae.Light:"medium"===e?ae.Medium:ae.Heavy;await de.impact({style:t})}catch(t){}}static async setStatusBarColor(e,t=!0){if(this.isNative())try{await Z.setBackgroundColor({color:e}),await Z.setStyle({style:t?K.Dark:K.Light})}catch(r){}}static getViewportHeight(){return this.isNative()?"100vh":"100dvh"}static addTouchClass(e){e.classList.add("touch-target")}static isTouchDevice(){return"ontouchstart"in window||navigator.maxTouchPoints>0}}const pe=B("App",{web:()=>L(()=>import("./web-BfGi1d1w.js"),__vite__mapDeps([7,1,2,3,4,5])).then(e=>new e.AppWeb)});class me{static instance;appInfo=null;constructor(){}static getInstance(){return me.instance||(me.instance=new me),me.instance}async initialize(){if(G.isNativePlatform())try{this.appInfo=await pe.getInfo(),await this.configureStatusBar(),await this.setupKeyboardListeners(),await this.setupAppStateListeners(),await ee.hide()}catch(e){}}async configureStatusBar(){try{await Z.setStyle({style:K.Dark}),await Z.setBackgroundColor({color:"#000000"})}catch(e){}}async setupKeyboardListeners(){try{se.addListener("keyboardWillShow",e=>{document.body.classList.add("keyboard-open"),document.body.style.setProperty("--keyboard-height",`${e.keyboardHeight}px`)}),se.addListener("keyboardWillHide",()=>{document.body.classList.remove("keyboard-open"),document.body.style.removeProperty("--keyboard-height")})}catch(e){}}async setupAppStateListeners(){try{pe.addListener("appStateChange",({isActive:e})=>{e?this.onAppResume():this.onAppPause()}),pe.addListener("backButton",({canGoBack:e})=>{e?window.history.back():pe.exitApp()})}catch(e){}}onAppResume(){window.dispatchEvent(new CustomEvent("app-resume"))}onAppPause(){window.dispatchEvent(new CustomEvent("app-pause"))}async provideFeedback(e="light"){if(G.isNativePlatform())try{switch(e){case"light":await de.impact({style:ae.Light});break;case"medium":await de.impact({style:ae.Medium});break;case"heavy":await de.impact({style:ae.Heavy});break;case"success":await de.notification({type:le.Success});break;case"warning":await de.notification({type:le.Warning});break;case"error":await de.notification({type:le.Error})}}catch(t){}}async setStatusBarColor(e,t=!0){if(G.isNativePlatform())try{await Z.setBackgroundColor({color:e}),await Z.setStyle({style:t?K.Dark:K.Light})}catch(r){}}getAppInfo(){return this.appInfo}isNative(){return G.isNativePlatform()}getPlatform(){return G.getPlatform()}async exitApp(){if(G.isNativePlatform())try{await pe.exitApp()}catch(e){}}}const fe=me.getInstance();function he(e){var t,r,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(r=he(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function be(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=he(e))&&(o&&(o+=" "),o+=t);return o}const ge=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,ve=be,xe=(e,t)=>r=>{var o;if(null==(null==t?void 0:t.variants))return ve(e,null==r?void 0:r.class,null==r?void 0:r.className);const{variants:n,defaultVariants:s}=t,a=Object.keys(n).map(e=>{const t=null==r?void 0:r[e],o=null==s?void 0:s[e];if(null===t)return null;const a=ge(t)||ge(o);return n[e][a]}),i=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{}),l=null==t||null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...i}[t]):{...s,...i}[t]===r})?[...e,r,o]:e},[]);return ve(e,a,l,null==r?void 0:r.class,null==r?void 0:r.className)},ye=e=>{const t=ke(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{const r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),we(r,t)||Ne(e)},getConflictingClassGroupIds:(e,t)=>{const n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},we=(e,t)=>{if(0===e.length)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),n=o?we(e.slice(1),o):void 0;if(n)return n;if(0===t.validators.length)return;const s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},je=/^\[(.+)\]$/,Ne=e=>{if(je.test(e)){const t=je.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},ke=e=>{const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return Se(Object.entries(e.classGroups),r).forEach(([e,r])=>{Ee(r,o,e,t)}),o},Ee=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){return void((""===e?t:Pe(t,e)).classGroupId=r)}if("function"==typeof e)return Ae(e)?void Ee(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{Ee(n,Pe(t,e),r,o)})})},Pe=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},Ae=e=>e.isThemeGetter,Se=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,Ce=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const n=(n,s)=>{r.set(n,s),t++,t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},Le=e=>{const{separator:t,experimentalParseClassName:r}=e,o=1===t.length,n=t[0],s=t.length,a=e=>{const r=[];let a,i=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===i){if(c===n&&(o||e.slice(u,u+s)===t)){r.push(e.slice(l,u)),l=u+s;continue}if("/"===c){a=u;continue}}"["===c?i++:"]"===c&&i--}const c=0===r.length?e:e.substring(l),d=c.startsWith("!");return{modifiers:r,hasImportantModifier:d,baseClassName:d?c.substring(1):c,maybePostfixModifierPosition:a&&a>l?a-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},Ie=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},_e=/\s+/;function Oe(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=Te(e))&&(o&&(o+=" "),o+=t);return o}const Te=e=>{if("string"==typeof e)return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=Te(e[o]))&&(r&&(r+=" "),r+=t);return r};function Re(e,...t){let r,o,n,s=function(i){const l=t.reduce((e,t)=>t(e),e());return r=(e=>({cache:Ce(e.cacheSize),parseClassName:Le(e),...ye(e)}))(l),o=r.cache.get,n=r.cache.set,s=a,a(i)};function a(e){const t=o(e);if(t)return t;const s=((e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=t,s=[],a=e.trim().split(_e);let i="";for(let l=a.length-1;l>=0;l-=1){const e=a[l],{modifiers:t,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(e);let p=Boolean(u),m=o(p?d.substring(0,u):d);if(!m){if(!p){i=e+(i.length>0?" "+i:i);continue}if(m=o(d),!m){i=e+(i.length>0?" "+i:i);continue}p=!1}const f=Ie(t).join(":"),h=c?f+"!":f,b=h+m;if(s.includes(b))continue;s.push(b);const g=n(m,p);for(let r=0;r<g.length;++r){const e=g[r];s.push(h+e)}i=e+(i.length>0?" "+i:i)}return i})(e,r);return n(e,s),s}return function(){return s(Oe.apply(null,arguments))}}const ze=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},De=/^\[(?:([a-z-]+):)?(.+)\]$/i,Me=/^\d+\/\d+$/,$e=new Set(["px","full","screen"]),Ve=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ge=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Be=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ue=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,He=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,We=e=>Xe(e)||$e.has(e)||Me.test(e),Fe=e=>at(e,"length",it),Xe=e=>Boolean(e)&&!Number.isNaN(Number(e)),qe=e=>at(e,"number",Xe),Ke=e=>Boolean(e)&&Number.isInteger(Number(e)),Ye=e=>e.endsWith("%")&&Xe(e.slice(0,-1)),Je=e=>De.test(e),Qe=e=>Ve.test(e),Ze=new Set(["length","size","percentage"]),et=e=>at(e,Ze,lt),tt=e=>at(e,"position",lt),rt=new Set(["image","url"]),ot=e=>at(e,rt,dt),nt=e=>at(e,"",ct),st=()=>!0,at=(e,t,r)=>{const o=De.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},it=e=>Ge.test(e)&&!Be.test(e),lt=()=>!1,ct=e=>Ue.test(e),dt=e=>He.test(e),ut=Re(()=>{const e=ze("colors"),t=ze("spacing"),r=ze("blur"),o=ze("brightness"),n=ze("borderColor"),s=ze("borderRadius"),a=ze("borderSpacing"),i=ze("borderWidth"),l=ze("contrast"),c=ze("grayscale"),d=ze("hueRotate"),u=ze("invert"),p=ze("gap"),m=ze("gradientColorStops"),f=ze("gradientColorStopPositions"),h=ze("inset"),b=ze("margin"),g=ze("opacity"),v=ze("padding"),x=ze("saturate"),y=ze("scale"),w=ze("sepia"),j=ze("skew"),N=ze("space"),k=ze("translate"),E=()=>["auto",Je,t],P=()=>[Je,t],A=()=>["",We,Fe],S=()=>["auto",Xe,Je],C=()=>["","0",Je],L=()=>[Xe,Je];return{cacheSize:500,separator:":",theme:{colors:[st],spacing:[We,Fe],blur:["none","",Qe,Je],brightness:L(),borderColor:[e],borderRadius:["none","","full",Qe,Je],borderSpacing:P(),borderWidth:A(),contrast:L(),grayscale:C(),hueRotate:L(),invert:C(),gap:P(),gradientColorStops:[e],gradientColorStopPositions:[Ye,Fe],inset:E(),margin:E(),opacity:L(),padding:P(),saturate:L(),scale:L(),sepia:C(),skew:L(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",Je]}],container:["container"],columns:[{columns:[Qe]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Je]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ke,Je]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Je]}],grow:[{grow:C()}],shrink:[{shrink:C()}],order:[{order:["first","last","none",Ke,Je]}],"grid-cols":[{"grid-cols":[st]}],"col-start-end":[{col:["auto",{span:["full",Ke,Je]},Je]}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":[st]}],"row-start-end":[{row:["auto",{span:[Ke,Je]},Je]}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Je]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Je]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Je,t]}],"min-w":[{"min-w":[Je,t,"min","max","fit"]}],"max-w":[{"max-w":[Je,t,"none","full","min","max","fit","prose",{screen:[Qe]},Qe]}],h:[{h:[Je,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Je,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Je,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Je,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Qe,Fe]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",qe]}],"font-family":[{font:[st]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Je]}],"line-clamp":[{"line-clamp":["none",Xe,qe]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",We,Je]}],"list-image":[{"list-image":["none",Je]}],"list-style-type":[{list:["none","disc","decimal",Je]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",We,Fe]}],"underline-offset":[{"underline-offset":["auto",We,Je]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Je]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Je]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",tt]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",et]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ot]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[We,Je]}],"outline-w":[{outline:[We,Fe]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:A()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[We,Fe]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Qe,nt]}],"shadow-color":[{shadow:[st]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Qe,Je]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[x]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Je]}],duration:[{duration:L()}],ease:[{ease:["linear","in","out","in-out",Je]}],delay:[{delay:L()}],animate:[{animate:["none","spin","ping","pulse","bounce",Je]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[Ke,Je]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Je]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Je]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Je]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[We,Fe,qe]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function pt(...e){return ut(be(e))}function mt(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}function ft(e){return new Intl.NumberFormat("en-US").format(e)}function ht(e){const t=new Date,r="string"==typeof e?new Date(e):e,o=Math.floor((t.getTime()-r.getTime())/1e3);if(o<60)return"just now";if(o<3600){const e=Math.floor(o/60);return`${e} minute${e>1?"s":""} ago`}if(o<86400){const e=Math.floor(o/3600);return`${e} hour${e>1?"s":""} ago`}if(o<2592e3){const e=Math.floor(o/86400);return`${e} day${e>1?"s":""} ago`}return r.toLocaleDateString()}const bt=xe("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),gt=r.forwardRef(({className:r,variant:o,size:n,asChild:s=!1,...a},i)=>{const l=s?t:"button";return e.jsx(l,{className:pt(bt({variant:o,size:n,className:r})),ref:i,...a})});gt.displayName="Button";const vt=r.forwardRef(({className:t,...r},o)=>e.jsx("div",{ref:o,className:pt("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r}));vt.displayName="Card";const xt=r.forwardRef(({className:t,...r},o)=>e.jsx("div",{ref:o,className:pt("flex flex-col space-y-1.5 p-6",t),...r}));xt.displayName="CardHeader";const yt=r.forwardRef(({className:t,...r},o)=>e.jsx("h3",{ref:o,className:pt("text-2xl font-semibold leading-none tracking-tight",t),...r}));yt.displayName="CardTitle";r.forwardRef(({className:t,...r},o)=>e.jsx("p",{ref:o,className:pt("text-sm text-muted-foreground",t),...r})).displayName="CardDescription";const wt=r.forwardRef(({className:t,...r},o)=>e.jsx("div",{ref:o,className:pt("p-6 pt-0",t),...r}));wt.displayName="CardContent";r.forwardRef(({className:t,...r},o)=>e.jsx("div",{ref:o,className:pt("flex items-center p-6 pt-0",t),...r})).displayName="CardFooter";class jt extends r.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t})}handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})};handleGoHome=()=>{window.location.href="/"};render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 bg-background",children:e.jsxs(vt,{className:"w-full max-w-md",children:[e.jsxs(xt,{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:e.jsx(f,{className:"w-6 h-6 text-red-600"})}),e.jsx(yt,{className:"text-xl",children:"Something went wrong"})]}),e.jsxs(wt,{className:"space-y-4",children:[e.jsx("p",{className:"text-center text-muted-foreground",children:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the homepage."}),!1,e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsxs(gt,{onClick:this.handleRetry,className:"flex-1",variant:"outline",children:[e.jsx(h,{className:"w-4 h-4 mr-2"}),"Try Again"]}),e.jsxs(gt,{onClick:this.handleGoHome,className:"flex-1",children:[e.jsx(b,{className:"w-4 h-4 mr-2"}),"Go Home"]})]})]})]})}):this.props.children}}const Nt=({text:t="Loading page..."})=>e.jsxs("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"auto-scouter-gradient w-16 h-16 rounded-full flex items-center justify-center animate-pulse",children:e.jsx(g,{className:"w-8 h-8 text-white"})}),e.jsx(v,{className:"absolute inset-0 w-16 h-16 animate-spin text-primary"})]}),e.jsx("p",{className:"text-lg font-medium",children:t}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Please wait while we load your content"})]}),kt=xe("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function Et({className:t,variant:r,...o}){return e.jsx("div",{className:pt(kt({variant:r}),t),...o})}const Pt=[{name:"Dashboard",href:"/dashboard",icon:x},{name:"Vehicle Search",href:"/search",icon:y},{name:"My Alerts",href:"/alerts",icon:f,badge:"5"},{name:"Notifications",href:"/notifications",icon:w,badge:"3"}],At=()=>e.jsx("aside",{className:"w-64 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60","aria-label":"Main navigation",children:e.jsx("nav",{className:"flex flex-col space-y-1 p-4",role:"navigation",children:Pt.map(t=>{const r=t.icon;return e.jsxs(o,{to:t.href,className:({isActive:e})=>pt("flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors","focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",e?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(r,{className:"h-4 w-4","aria-hidden":"true"}),e.jsx("span",{children:t.name})]}),t.badge&&e.jsx(Et,{variant:"secondary",className:"ml-auto",children:t.badge})]},t.name)})})}),St=r.forwardRef(({className:t,type:r,...o},n)=>e.jsx("input",{type:r,className:pt("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:n,...o}));St.displayName="Input";const Ct=({onMenuClick:t})=>e.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:e.jsxs("div",{className:"flex h-16 items-center px-4 lg:px-6",children:[e.jsx(gt,{variant:"ghost",size:"icon",className:"lg:hidden mr-2",onClick:t,"aria-label":"Open navigation menu",children:e.jsx(j,{className:"h-5 w-5"})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"auto-scouter-gradient h-8 w-8 rounded-lg flex items-center justify-center",children:e.jsx(g,{className:"h-5 w-5 text-white"})}),e.jsx("h1",{className:"text-xl font-bold",children:"Petrit's Vehicle Scout"})]}),e.jsx("div",{className:"flex-1 max-w-md mx-4 lg:mx-8",children:e.jsxs("div",{className:"relative",children:[e.jsx(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(St,{placeholder:"Search vehicles, alerts...",className:"pl-10","aria-label":"Global search"})]})}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(n,{to:"/notifications",children:e.jsxs(gt,{variant:"ghost",size:"icon",className:"relative",children:[e.jsx(w,{className:"h-5 w-5"}),e.jsx(Et,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:"3"})]})}),e.jsxs("div",{className:"hidden sm:block",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Welcome back, "}),e.jsx("span",{className:"text-sm font-medium",children:"Petrit"})]})]})]})}),Lt=[{name:"Dashboard",href:"/dashboard",icon:x},{name:"Vehicle Search",href:"/search",icon:y},{name:"My Alerts",href:"/alerts",icon:f,badge:"5"},{name:"Notifications",href:"/notifications",icon:w,badge:"3"},{name:"Saved Vehicles",href:"/saved",icon:g},{name:"Analytics",href:"/analytics",icon:k},{name:"Settings",href:"/settings",icon:E}],It=({isOpen:t,onClose:r})=>t?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:r,"aria-hidden":"true"}),e.jsxs("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-background border-r shadow-lg lg:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"auto-scouter-gradient h-8 w-8 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"AS"})}),e.jsx("h1",{className:"text-lg font-bold",children:"Auto Scouter"})]}),e.jsx(gt,{variant:"ghost",size:"icon",onClick:r,"aria-label":"Close navigation menu",children:e.jsx(N,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"flex flex-col space-y-1 p-4",role:"navigation",children:Lt.map(t=>{const n=t.icon;return e.jsxs(o,{to:t.href,onClick:r,className:({isActive:e})=>pt("flex items-center justify-between rounded-lg px-3 py-3 text-sm font-medium transition-colors","focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",e?"bg-primary text-primary-foreground":"text-muted-foreground hover:bg-accent hover:text-accent-foreground"),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(n,{className:"h-5 w-5","aria-hidden":"true"}),e.jsx("span",{children:t.name})]}),t.badge&&e.jsx(Et,{variant:"secondary",className:"ml-auto",children:t.badge})]},t.name)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t bg-muted/50",children:e.jsxs("div",{className:"text-center text-xs text-muted-foreground",children:[e.jsx("p",{children:"Auto Scouter v1.0"}),e.jsx("p",{children:"© 2024 Auto Scouter"})]})})]})]}):null,_t=({children:t})=>{const[o,n]=r.useState(!1);return e.jsxs("div",{className:"mobile-viewport bg-background mobile-safe-area",children:[e.jsx(Ct,{onMenuClick:()=>n(!0)}),e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"hidden lg:block",children:e.jsx(At,{})}),e.jsx(It,{isOpen:o,onClose:()=>n(!1)}),e.jsx("main",{className:"flex-1 p-4 lg:p-6 min-h-[calc(100vh-4rem)] mobile-scroll",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:t})})]})]})},Ot=s.lazy(()=>L(()=>import("./Dashboard-DW2SyjMw.js"),__vite__mapDeps([8,1,2,3,9,4,10,5,11])).then(e=>({default:e.Dashboard}))),Tt=s.lazy(()=>L(()=>import("./VehicleSearch-DwNl0vTT.js"),__vite__mapDeps([12,1,2,3,9,4,10,5,13,14])).then(e=>({default:e.VehicleSearch}))),Rt=s.lazy(()=>L(()=>import("./VehicleDetail-DOj6uqiX.js"),__vite__mapDeps([15,1,2,3,13,5,4])).then(e=>({default:e.VehicleDetail}))),zt=s.lazy(()=>L(()=>import("./AlertManager-DIHB6BvX.js"),__vite__mapDeps([16,1,2,3,11,17,5,14,10,13,4])).then(e=>({default:e.AlertManager}))),Dt=s.lazy(()=>L(()=>import("./NotificationCenter-DvqP_5ur.js"),__vite__mapDeps([18,1,2,3,13,17,5,10,14,11,4])).then(e=>({default:e.NotificationCenter}))),Mt=new p({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3,refetchOnWindowFocus:!1},mutations:{retry:1}}});function $t(){return r.useEffect(()=>{(async()=>{await fe.initialize(),await ue.initializeApp(),R.logUserAction("app_start",{version:_,environment:O,platform:fe.isNative()?"mobile":"web",appInfo:fe.getAppInfo()})})()},[]),e.jsx(m,{client:Mt,children:e.jsx(jt,{children:e.jsx(a,{children:e.jsx(r.Suspense,{fallback:e.jsx(Nt,{}),children:e.jsxs(i,{children:[e.jsx(l,{path:"/",element:e.jsx(c,{to:"/dashboard",replace:!0})}),e.jsx(l,{path:"/dashboard",element:e.jsx(_t,{children:e.jsx(Ot,{})})}),e.jsx(l,{path:"/search",element:e.jsx(_t,{children:e.jsx(Tt,{})})}),e.jsx(l,{path:"/vehicle/:id",element:e.jsx(_t,{children:e.jsx(Rt,{vehicleId:"",onBack:()=>window.history.back()})})}),e.jsx(l,{path:"/alerts",element:e.jsx(_t,{children:e.jsx(zt,{})})}),e.jsx(l,{path:"/notifications",element:e.jsx(_t,{children:e.jsx(Dt,{})})}),e.jsx(l,{path:"*",element:e.jsx(c,{to:"/dashboard",replace:!0})})]})})})})})}"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(e=>{}).catch(e=>{})}),S.createRoot(document.getElementById("root")).render(e.jsx(s.StrictMode,{children:e.jsx($t,{})}));export{gt as B,vt as C,St as I,le as N,U as W,Et as a,xt as b,yt as c,wt as d,ft as e,ht as f,mt as g,pt as h,ae as i};
