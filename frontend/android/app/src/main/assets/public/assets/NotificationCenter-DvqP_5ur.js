import{j as e}from"./ui-B-v3Zwus.js";import{r as a}from"./router-B-XVe37S.js";import{C as t,a as s,B as n,b as i,c as r,d as l,I as c}from"./index-BNatCL7N.js";import{T as o,a as d,b as m,c as u}from"./tabs-CVrXG0tV.js";import{D as h,a as x,b as f,c as p}from"./dropdown-menu-Dvsgogy5.js";import{a as j}from"./api-BK_ENMzF.js";import{C as g,J as y,q as v,l as b,w as N,K as w,N as k,F as C,X as M,g as S,c as D,u as T,O as P,B as F,V as A,m as W,z as q}from"./icons-DaMsayX9.js";import{D as _,a as R,b as E,c as z}from"./dialog-D3B6nCUe.js";import{S as X}from"./switch-DppGFgFi.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";function H(e){return(H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function U(e,a){if(a.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+a.length+" present")}function Y(e){U(1,arguments);var a=Object.prototype.toString.call(e);return e instanceof Date||"object"===H(e)&&"[object Date]"===a?new Date(e.getTime()):"number"==typeof e||"[object Number]"===a?new Date(e):new Date(NaN)}var O={};function $(e){var a=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return a.setUTCFullYear(e.getFullYear()),e.getTime()-a.getTime()}function J(e,a){U(2,arguments);var t=Y(e),s=Y(a),n=t.getTime()-s.getTime();return n<0?-1:n>0?1:n}var I={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(e){return e<0?Math.ceil(e):Math.floor(e)}};function V(e){U(1,arguments);var a=Y(e);return function(e){U(1,arguments);var a=Y(e);return a.setHours(23,59,59,999),a}(a).getTime()===function(e){U(1,arguments);var a=Y(e),t=a.getMonth();return a.setFullYear(a.getFullYear(),t+1,0),a.setHours(23,59,59,999),a}(a).getTime()}function B(e,a){U(2,arguments);var t,s=Y(e),n=Y(a),i=J(s,n),r=Math.abs(function(e,a){U(2,arguments);var t=Y(e),s=Y(a);return 12*(t.getFullYear()-s.getFullYear())+(t.getMonth()-s.getMonth())}(s,n));if(r<1)t=0;else{1===s.getMonth()&&s.getDate()>27&&s.setDate(30),s.setMonth(s.getMonth()-i*r);var l=J(s,n)===-i;V(Y(e))&&1===r&&1===J(e,n)&&(l=!1),t=i*(r-Number(l))}return 0===t?0:t}function Q(e,a,t){U(2,arguments);var s=function(e,a){return U(2,arguments),Y(e).getTime()-Y(a).getTime()}(e,a)/1e3;return I.trunc(s)}var L={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function G(e){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=a.width?String(a.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}var K={date:G({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:G({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:G({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Z={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function ee(e){return function(a,t){var s;if("formatting"===(null!=t&&t.context?String(t.context):"standalone")&&e.formattingValues){var n=e.defaultFormattingWidth||e.defaultWidth,i=null!=t&&t.width?String(t.width):n;s=e.formattingValues[i]||e.formattingValues[n]}else{var r=e.defaultWidth,l=null!=t&&t.width?String(t.width):e.defaultWidth;s=e.values[l]||e.values[r]}return s[e.argumentCallback?e.argumentCallback(a):a]}}function ae(e){return function(a){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=t.width,n=s&&e.matchPatterns[s]||e.matchPatterns[e.defaultMatchWidth],i=a.match(n);if(!i)return null;var r,l=i[0],c=s&&e.parsePatterns[s]||e.parsePatterns[e.defaultParseWidth],o=Array.isArray(c)?function(e,a){for(var t=0;t<e.length;t++)if(a(e[t]))return t;return}(c,function(e){return e.test(l)}):function(e,a){for(var t in e)if(e.hasOwnProperty(t)&&a(e[t]))return t;return}(c,function(e){return e.test(l)});return r=e.valueCallback?e.valueCallback(o):o,{value:r=t.valueCallback?t.valueCallback(r):r,rest:a.slice(l.length)}}}var te,se={code:"en-US",formatDistance:function(e,a,t){var s,n=L[e];return s="string"==typeof n?n:1===a?n.one:n.other.replace("{{count}}",a.toString()),null!=t&&t.addSuffix?t.comparison&&t.comparison>0?"in "+s:s+" ago":s},formatLong:K,formatRelative:function(e,a,t,s){return Z[e]},localize:{ordinalNumber:function(e,a){var t=Number(e),s=t%100;if(s>20||s<10)switch(s%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},era:ee({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:ee({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:ee({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:ee({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:ee({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(te={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.match(te.matchPattern);if(!t)return null;var s=t[0],n=e.match(te.parsePattern);if(!n)return null;var i=te.valueCallback?te.valueCallback(n[0]):n[0];return{value:i=a.valueCallback?a.valueCallback(i):i,rest:e.slice(s.length)}}),era:ae({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:ae({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ae({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:ae({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:ae({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function ne(e,a){if(null==e)throw new TypeError("assign requires that input parameter not be null or undefined");for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t]);return e}var ie=43200;function re(e,a,t){var s,n;U(2,arguments);var i=O,r=null!==(s=null!==(n=null==t?void 0:t.locale)&&void 0!==n?n:i.locale)&&void 0!==s?s:se;if(!r.formatDistance)throw new RangeError("locale must contain formatDistance property");var l=J(e,a);if(isNaN(l))throw new RangeError("Invalid time value");var c,o,d=ne(ne({},t),{addSuffix:Boolean(null==t?void 0:t.addSuffix),comparison:l});l>0?(c=Y(a),o=Y(e)):(c=Y(e),o=Y(a));var m,u=Q(o,c),h=($(o)-$(c))/1e3,x=Math.round((u-h)/60);if(x<2)return null!=t&&t.includeSeconds?u<5?r.formatDistance("lessThanXSeconds",5,d):u<10?r.formatDistance("lessThanXSeconds",10,d):u<20?r.formatDistance("lessThanXSeconds",20,d):u<40?r.formatDistance("halfAMinute",0,d):u<60?r.formatDistance("lessThanXMinutes",1,d):r.formatDistance("xMinutes",1,d):0===x?r.formatDistance("lessThanXMinutes",1,d):r.formatDistance("xMinutes",x,d);if(x<45)return r.formatDistance("xMinutes",x,d);if(x<90)return r.formatDistance("aboutXHours",1,d);if(x<1440){var f=Math.round(x/60);return r.formatDistance("aboutXHours",f,d)}if(x<2520)return r.formatDistance("xDays",1,d);if(x<ie){var p=Math.round(x/1440);return r.formatDistance("xDays",p,d)}if(x<86400)return m=Math.round(x/ie),r.formatDistance("aboutXMonths",m,d);if((m=B(o,c))<12){var j=Math.round(x/ie);return r.formatDistance("xMonths",j,d)}var g=m%12,y=Math.floor(m/12);return g<3?r.formatDistance("aboutXYears",y,d):g<9?r.formatDistance("overXYears",y,d):r.formatDistance("almostXYears",y+1,d)}function le(e,a){return U(1,arguments),re(e,Date.now(),a)}const ce=({notification:a,onAction:i})=>{const{content_data:r}=a,l=r?.listing,c=r?.alert,o=r?.match;return e.jsx("div",{className:`p-4 border-l-4 ${(e=>{switch(e){case 5:case 4:return"border-l-red-500";case 3:return"border-l-orange-500";case 2:return"border-l-blue-500";default:return"border-l-green-500"}})(a.priority)} ${a.is_read?"bg-white":"bg-blue-50"} hover:bg-gray-50 transition-colors`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[(a=>{switch(a){case 5:case 4:return e.jsx(k,{className:"h-4 w-4 text-red-500"});case 3:return e.jsx(k,{className:"h-4 w-4 text-orange-500"});case 2:return e.jsx(w,{className:"h-4 w-4 text-blue-500"});default:return e.jsx(N,{className:"h-4 w-4 text-green-500"})}})(a.priority),e.jsx("h3",{className:"text-sm font-medium "+(a.is_read?"text-gray-700":"text-gray-900"),children:a.title}),!a.is_read&&e.jsx("div",{className:"w-2 h-2 bg-blue-600 rounded-full"})]}),l&&e.jsx(t,{className:"mb-3 p-3 bg-gray-50",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[l.primary_image_url?e.jsx("img",{src:l.primary_image_url,alt:`${l.make} ${l.model}`,className:"w-16 h-12 object-cover rounded"}):e.jsx("div",{className:"w-16 h-12 bg-gray-200 rounded flex items-center justify-center",children:e.jsx(g,{className:"h-6 w-6 text-gray-400"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:[l.make," ",l.model]}),l.year&&e.jsx(s,{variant:"secondary",className:"text-xs",children:l.year})]}),e.jsxs("div",{className:"flex items-center space-x-4 mt-1 text-sm text-gray-600",children:[l.price&&e.jsx("span",{className:"font-semibold text-green-600",children:(d=l.price,new Intl.NumberFormat("en-EU",{style:"currency",currency:"EUR",maximumFractionDigits:0}).format(d))}),l.city&&e.jsx("span",{children:l.city})]}),o&&e.jsx("div",{className:"mt-2",children:e.jsx(s,{variant:o.is_perfect_match?"default":"secondary",className:"text-xs",children:o.is_perfect_match?"Perfect Match":`${Math.round(100*o.score)}% Match`})})]})]})}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:a.message}),c&&e.jsxs("div",{className:"text-xs text-gray-500 mb-2",children:["Alert: ",e.jsx("span",{className:"font-medium",children:c.name})]}),e.jsx("div",{className:"text-xs text-gray-400",children:le(new Date(a.created_at),{addSuffix:!0})})]}),e.jsxs("div",{className:"flex flex-col space-y-1 ml-4",children:[!a.is_read&&e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>i(a.id,"read"),className:"h-8 w-8 p-0",children:e.jsx(y,{className:"h-4 w-4"})}),l?.listing_url&&e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>{l?.listing_url&&window.open(l.listing_url,"_blank")},className:"h-8 w-8 p-0",children:e.jsx(v,{className:"h-4 w-4"})}),e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>i(a.id,"delete"),className:"h-8 w-8 p-0 text-red-500 hover:text-red-700",children:e.jsx(b,{className:"h-4 w-4"})})]})]})});var d},oe=[{value:"new_match",label:"New Match"},{value:"price_drop",label:"Price Drop"},{value:"alert_triggered",label:"Alert Triggered"},{value:"system",label:"System"}],de=[{value:!0,label:"Read"},{value:!1,label:"Unread"}],me=({filters:a,onFiltersChange:o,onClose:d})=>{const m=(e,t)=>{o({...a,[e]:t})},u=Object.values(a).some(e=>null!=e);return e.jsxs(t,{className:"border-2 border-primary/20",children:[e.jsx(i,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(r,{className:"text-lg flex items-center",children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"Filter Notifications"]}),e.jsxs("div",{className:"flex space-x-2",children:[u&&e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>{o({type:null,status:null,isRead:null,dateFrom:null,dateTo:null})},children:"Clear All"}),e.jsx(n,{variant:"ghost",size:"sm",onClick:d,children:e.jsx(M,{className:"h-4 w-4"})})]})]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Notification Type"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:oe.map(t=>e.jsx(s,{variant:a.type===t.value?"default":"outline",className:"cursor-pointer",onClick:()=>m("type",a.type===t.value?null:t.value),children:t.label},t.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Read Status"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:de.map(t=>e.jsx(s,{variant:a.isRead===t.value?"default":"outline",className:"cursor-pointer",onClick:()=>m("isRead",a.isRead===t.value?null:t.value),children:t.label},t.value.toString()))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm font-medium mb-2 block flex items-center",children:[e.jsx(S,{className:"mr-1 h-4 w-4"}),"Date Range"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-muted-foreground mb-1 block",children:"From"}),e.jsx(c,{type:"date",value:a.dateFrom||"",onChange:e=>m("dateFrom",e.target.value||null)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-muted-foreground mb-1 block",children:"To"}),e.jsx(c,{type:"date",value:a.dateTo||"",onChange:e=>m("dateTo",e.target.value||null)})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Priority"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(s,{variant:"high"===a.status?"default":"outline",className:"cursor-pointer",onClick:()=>m("status","high"===a.status?null:"high"),children:"High Priority"}),e.jsx(s,{variant:"medium"===a.status?"default":"outline",className:"cursor-pointer",onClick:()=>m("status","medium"===a.status?null:"medium"),children:"Medium Priority"}),e.jsx(s,{variant:"low"===a.status?"default":"outline",className:"cursor-pointer",onClick:()=>m("status","low"===a.status?null:"low"),children:"Low Priority"})]})]}),u&&e.jsxs("div",{className:"pt-2 border-t",children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Active Filters:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[a.type&&e.jsxs(s,{variant:"secondary",className:"text-xs",children:["Type: ",oe.find(e=>e.value===a.type)?.label]}),null!==a.isRead&&e.jsxs(s,{variant:"secondary",className:"text-xs",children:["Status: ",a.isRead?"Read":"Unread"]}),a.dateFrom&&e.jsxs(s,{variant:"secondary",className:"text-xs",children:["From: ",a.dateFrom]}),a.dateTo&&e.jsxs(s,{variant:"secondary",className:"text-xs",children:["To: ",a.dateTo]}),a.status&&e.jsxs(s,{variant:"secondary",className:"text-xs",children:["Priority: ",a.status]})]})]})]})]})},ue=({isOpen:s,onClose:c})=>{const[h,x]=a.useState({email:{enabled:!0,newMatches:!0,priceDrops:!0,weeklyDigest:!0,systemUpdates:!1},push:{enabled:!0,newMatches:!0,priceDrops:!0,systemUpdates:!1},inApp:{enabled:!0,sound:!0,desktop:!0},frequency:{immediate:!0,daily:!1,weekly:!1},quietHours:{enabled:!1,startTime:"22:00",endTime:"08:00"}}),f=(e,a,t)=>{x(s=>({...s,[e]:{...s[e],[a]:t}}))};return e.jsx(_,{open:s,onOpenChange:c,children:e.jsxs(R,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[e.jsx(E,{children:e.jsxs(z,{className:"flex items-center",children:[e.jsx(D,{className:"mr-2 h-5 w-5"}),"Notification Preferences"]})}),e.jsxs(o,{defaultValue:"channels",className:"w-full",children:[e.jsxs(d,{className:"grid w-full grid-cols-3",children:[e.jsx(m,{value:"channels",children:"Channels"}),e.jsx(m,{value:"types",children:"Types"}),e.jsx(m,{value:"schedule",children:"Schedule"})]}),e.jsxs(u,{value:"channels",className:"space-y-4 mt-6",children:[e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"text-lg flex items-center",children:[e.jsx(T,{className:"mr-2 h-5 w-5"}),"Email Notifications"]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Enable Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),e.jsx(X,{checked:h.email.enabled,onCheckedChange:e=>f("email","enabled",e)})]}),h.email.enabled&&e.jsxs("div",{className:"space-y-3 pl-4 border-l-2 border-muted",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"New vehicle matches"}),e.jsx(X,{checked:h.email.newMatches,onCheckedChange:e=>f("email","newMatches",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Price drop alerts"}),e.jsx(X,{checked:h.email.priceDrops,onCheckedChange:e=>f("email","priceDrops",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Weekly digest"}),e.jsx(X,{checked:h.email.weeklyDigest,onCheckedChange:e=>f("email","weeklyDigest",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"System updates"}),e.jsx(X,{checked:h.email.systemUpdates,onCheckedChange:e=>f("email","systemUpdates",e)})]})]})]})]}),e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"text-lg flex items-center",children:[e.jsx(P,{className:"mr-2 h-5 w-5"}),"Push Notifications"]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Enable Push Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Receive notifications on your device"})]}),e.jsx(X,{checked:h.push.enabled,onCheckedChange:e=>f("push","enabled",e)})]}),h.push.enabled&&e.jsxs("div",{className:"space-y-3 pl-4 border-l-2 border-muted",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"New vehicle matches"}),e.jsx(X,{checked:h.push.newMatches,onCheckedChange:e=>f("push","newMatches",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Price drop alerts"}),e.jsx(X,{checked:h.push.priceDrops,onCheckedChange:e=>f("push","priceDrops",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"System updates"}),e.jsx(X,{checked:h.push.systemUpdates,onCheckedChange:e=>f("push","systemUpdates",e)})]})]})]})]}),e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"text-lg flex items-center",children:[e.jsx(F,{className:"mr-2 h-5 w-5"}),"In-App Notifications"]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Enable In-App Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Show notifications within the application"})]}),e.jsx(X,{checked:h.inApp.enabled,onCheckedChange:e=>f("inApp","enabled",e)})]}),h.inApp.enabled&&e.jsxs("div",{className:"space-y-3 pl-4 border-l-2 border-muted",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm flex items-center",children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Sound notifications"]}),e.jsx(X,{checked:h.inApp.sound,onCheckedChange:e=>f("inApp","sound",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Desktop notifications"}),e.jsx(X,{checked:h.inApp.desktop,onCheckedChange:e=>f("inApp","desktop",e)})]})]})]})]})]}),e.jsx(u,{value:"types",className:"space-y-4 mt-6",children:e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(r,{className:"text-lg",children:"Notification Types"})}),e.jsx(l,{className:"space-y-4",children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"p-4 border rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"New Vehicle Matches"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Get notified when new vehicles match your alerts"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"newMatches",checked:h.frequency.immediate,onChange:()=>{f("frequency","immediate",!0),f("frequency","daily",!1),f("frequency","weekly",!1)}}),e.jsx("span",{className:"text-sm",children:"Immediate"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"newMatches",checked:h.frequency.daily,onChange:()=>{f("frequency","immediate",!1),f("frequency","daily",!0),f("frequency","weekly",!1)}}),e.jsx("span",{className:"text-sm",children:"Daily digest"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"newMatches",checked:h.frequency.weekly,onChange:()=>{f("frequency","immediate",!1),f("frequency","daily",!1),f("frequency","weekly",!0)}}),e.jsx("span",{className:"text-sm",children:"Weekly digest"})]})]})]})})})]})}),e.jsx(u,{value:"schedule",className:"space-y-4 mt-6",children:e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(r,{className:"text-lg",children:"Quiet Hours"})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Enable Quiet Hours"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Pause notifications during specified hours"})]}),e.jsx(X,{checked:h.quietHours.enabled,onCheckedChange:e=>f("quietHours","enabled",e)})]}),h.quietHours.enabled&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 pl-4 border-l-2 border-muted",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Start Time"}),e.jsx("input",{type:"time",value:h.quietHours.startTime,onChange:e=>f("quietHours","startTime",e.target.value),className:"w-full border rounded px-3 py-2"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"End Time"}),e.jsx("input",{type:"time",value:h.quietHours.endTime,onChange:e=>f("quietHours","endTime",e.target.value),className:"w-full border rounded px-3 py-2"})]})]})]})]})})]}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-6",children:[e.jsx(n,{variant:"outline",onClick:c,children:"Cancel"}),e.jsxs(n,{onClick:()=>{c()},className:"auto-scouter-gradient",children:[e.jsx(W,{className:"mr-2 h-4 w-4"}),"Save Preferences"]})]})]})})},he=({isOpen:t=!0,onClose:i=()=>{}})=>{const[r,l]=a.useState("all"),[c,u]=a.useState(!1),[g,v]=a.useState(!1),[b,N]=a.useState({type:null,status:null,isRead:null,dateFrom:null,dateTo:null}),{notifications:w,unreadCount:k,loading:S,error:T,markAsRead:P,markAllAsRead:A,deleteNotification:W,fetchNotifications:_,fetchUnreadNotifications:R}=(()=>{const[e,t]=a.useState([]),[s,n]=a.useState(0),[i,r]=a.useState(!1),[l,c]=a.useState(null),[o,d]=a.useState({page:1,pageSize:20,totalCount:0,totalPages:0,hasNext:!1,hasPrev:!1}),m=a.useCallback(async(e={})=>{r(!0),c(null);try{const a=new URLSearchParams;e.type&&a.append("notification_type",e.type),e.status&&a.append("status",e.status),void 0!==e.isRead&&null!==e.isRead&&a.append("is_read",e.isRead.toString()),e.dateFrom&&a.append("date_from",e.dateFrom),e.dateTo&&a.append("date_to",e.dateTo),e.page&&a.append("page",e.page.toString()),e.pageSize&&a.append("page_size",e.pageSize.toString());const s=await j.get(`/notifications/?${a.toString()}`);t(s.data.notifications),d(s.data.pagination)}catch(a){c(a.response?.data?.detail||"Failed to fetch notifications")}finally{r(!1)}},[]),u=a.useCallback(async(e=50)=>{r(!0),c(null);try{const a=await j.get(`/notifications/unread?limit=${e}`);t(a.data)}catch(a){c(a.response?.data?.detail||"Failed to fetch unread notifications")}finally{r(!1)}},[]),h=a.useCallback(async()=>{try{await j.get("/notifications/unread?limit=1");const e=await j.get("/notifications/unread?limit=1000");n(e.data.length)}catch(e){}},[]),x=a.useCallback(async e=>{try{await j.post(`/notifications/${e}/mark-read`),t(a=>a.map(a=>a.id===e?{...a,is_read:!0}:a)),n(e=>Math.max(0,e-1))}catch(a){throw new Error(a.response?.data?.detail||"Failed to mark notification as read")}},[]),f=a.useCallback(async()=>{try{await j.post("/notifications/mark-all-read"),t(e=>e.map(e=>({...e,is_read:!0}))),n(0)}catch(e){throw new Error(e.response?.data?.detail||"Failed to mark all notifications as read")}},[]),p=a.useCallback(async a=>{try{await j.delete(`/notifications/${a}`);const s=e.find(e=>e.id===a);t(e=>e.filter(e=>e.id!==a)),s&&!s.is_read&&n(e=>Math.max(0,e-1))}catch(s){throw new Error(s.response?.data?.detail||"Failed to delete notification")}},[e]),g=a.useCallback(async e=>{try{await j.post(`/notifications/${e}/resend`)}catch(a){throw new Error(a.response?.data?.detail||"Failed to resend notification")}},[]),y=a.useCallback(async(e=30)=>{try{return(await j.get(`/notifications/stats?days=${e}`)).data}catch(a){throw new Error(a.response?.data?.detail||"Failed to fetch notification stats")}},[]),v=a.useCallback(async(e="email")=>{try{return(await j.post("/notifications/test",{notification_type:e})).data}catch(a){throw new Error(a.response?.data?.detail||"Failed to send test notification")}},[]);return a.useEffect(()=>{h();const e=setInterval(h,3e4);return()=>clearInterval(e)},[h]),a.useEffect(()=>{},[]),{notifications:e,unreadCount:s,loading:i,error:l,pagination:o,fetchNotifications:m,fetchUnreadNotifications:u,fetchUnreadCount:h,markAsRead:x,markAllAsRead:f,deleteNotification:p,resendNotification:g,getNotificationStats:y,sendTestNotification:v}})();a.useEffect(()=>{t&&("unread"===r?R():_(b))},[t,r,b]);const E=async(e,a)=>{try{"read"===a?await P(e):"delete"===a&&await W(e),"unread"===r?R():_(b)}catch(t){}},z=w.filter(e=>("unread"!==r||!e.is_read)&&(!("alerts"===r&&!e.alert_id)&&("system"!==r||!e.alert_id)));return t?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-end",children:e.jsxs("div",{className:"bg-white w-96 h-full shadow-xl overflow-hidden",children:[e.jsxs("div",{className:"border-b border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{className:"h-5 w-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold",children:"Notifications"}),k>0&&e.jsx(s,{variant:"destructive",className:"text-xs",children:k})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>u(!c),children:e.jsx(C,{className:"h-4 w-4"})}),e.jsxs(h,{children:[e.jsx(x,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"sm",children:e.jsx(q,{className:"h-4 w-4"})})}),e.jsxs(f,{align:"end",children:[e.jsxs(p,{onClick:async()=>{try{await A(),_(b)}catch(e){}},children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Mark all as read"]}),e.jsxs(p,{onClick:()=>v(!0),children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Preferences"]})]})]}),e.jsx(n,{variant:"ghost",size:"sm",onClick:i,children:e.jsx(M,{className:"h-4 w-4"})})]})]}),c&&e.jsx("div",{className:"mt-4",children:e.jsx(me,{filters:b,onFiltersChange:e=>N({type:e.type??null,status:e.status??null,isRead:e.isRead??null,dateFrom:e.dateFrom??null,dateTo:e.dateTo??null}),onClose:()=>u(!1)})})]}),e.jsxs(o,{value:r,onValueChange:l,className:"flex-1",children:[e.jsxs(d,{className:"grid w-full grid-cols-4 border-b",children:[e.jsx(m,{value:"all",children:"All"}),e.jsxs(m,{value:"unread",children:["Unread",k>0&&e.jsx(s,{variant:"secondary",className:"ml-1 text-xs",children:k})]}),e.jsx(m,{value:"alerts",children:"Alerts"}),e.jsx(m,{value:"system",children:"System"})]}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:S?e.jsx("div",{className:"flex items-center justify-center h-32",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):T?e.jsxs("div",{className:"p-4 text-center text-red-600",children:[e.jsx("p",{children:"Failed to load notifications"}),e.jsx(n,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>_(b),children:"Retry"})]}):0===z.length?e.jsxs("div",{className:"p-4 text-center text-gray-500",children:[e.jsx(F,{className:"h-12 w-12 mx-auto mb-2 text-gray-300"}),e.jsx("p",{children:"No notifications"}),e.jsx("p",{className:"text-sm",children:"You're all caught up!"})]}):e.jsx("div",{className:"divide-y divide-gray-100",children:z.map(a=>e.jsx(ce,{notification:a,onAction:E},a.id))})})]}),g&&e.jsx(ue,{isOpen:g,onClose:()=>v(!1)})]})}):null};export{he as NotificationCenter,he as default};
