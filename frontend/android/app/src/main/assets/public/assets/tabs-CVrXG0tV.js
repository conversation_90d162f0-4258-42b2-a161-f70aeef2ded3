import{r as e,j as s,s as a,t as i,v as t}from"./ui-B-v3Zwus.js";import{r as o}from"./router-B-XVe37S.js";import{h as r}from"./index-BNatCL7N.js";const n=e,f=o.forwardRef(({className:e,...i},t)=>s.jsx(a,{ref:t,className:r("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...i}));f.displayName=a.displayName;const d=o.forwardRef(({className:e,...a},t)=>s.jsx(i,{ref:t,className:r("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));d.displayName=i.displayName;const c=o.forwardRef(({className:e,...a},i)=>s.jsx(t,{ref:i,className:r("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=t.displayName;export{n as T,f as a,d as b,c};
