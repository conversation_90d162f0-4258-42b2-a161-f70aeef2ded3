import{u as e,g as r,j as t,h as a,i as s,k as n,l as o}from"./ui-B-v3Zwus.js";import{r as c}from"./router-B-XVe37S.js";import{h as i}from"./index-BNatCL7N.js";var d="Switch",[l,u]=a(d),[p,f]=l(d),b=c.forwardRef((a,o)=>{const{__scopeSwitch:i,name:l,checked:u,defaultChecked:f,required:b,disabled:h,value:m="on",onCheckedChange:g,form:w,...y}=a,[x,j]=c.useState(null),S=e(o,e=>j(e)),N=c.useRef(!1),C=!x||(w||!!x.closest("form")),[R,_]=r({prop:u,defaultProp:f??!1,onChange:g,caller:d});return t.jsxs(p,{scope:i,checked:R,disabled:h,children:[t.jsx(s.button,{type:"button",role:"switch","aria-checked":R,"aria-required":b,"data-state":v(R),"data-disabled":h?"":void 0,disabled:h,value:m,...y,ref:S,onClick:n(a.onClick,e=>{_(e=>!e),C&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),C&&t.jsx(k,{control:x,bubbles:!N.current,name:l,value:m,checked:R,required:b,disabled:h,form:w,style:{transform:"translateX(-100%)"}})]})});b.displayName=d;var h="SwitchThumb",m=c.forwardRef((e,r)=>{const{__scopeSwitch:a,...n}=e,o=f(h,a);return t.jsx(s.span,{"data-state":v(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:r})});m.displayName=h;var k=c.forwardRef(({__scopeSwitch:r,control:a,checked:s,bubbles:n=!0,...i},d)=>{const l=c.useRef(null),u=e(l,d),p=function(e){const r=c.useRef({value:e,previous:e});return c.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}(s),f=o(a);return c.useEffect(()=>{const e=l.current;if(!e)return;const r=window.HTMLInputElement.prototype,t=Object.getOwnPropertyDescriptor(r,"checked").set;if(p!==s&&t){const r=new Event("click",{bubbles:n});t.call(e,s),e.dispatchEvent(r)}},[p,s,n]),t.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...i,tabIndex:-1,ref:u,style:{...i.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}k.displayName="SwitchBubbleInput";var g=b,w=m;const y=c.forwardRef(({className:e,...r},a)=>t.jsx(g,{className:i("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...r,ref:a,children:t.jsx(w,{className:i("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));y.displayName=g.displayName;export{y as S};
