import{j as e}from"./ui-B-v3Zwus.js";import{r as s}from"./router-B-XVe37S.js";import{C as a,b as l,c as i,B as r,d as t,I as c,a as n,f as d}from"./index-BNatCL7N.js";import{u as m,V as o}from"./VehicleGrid-CmQ-CDxH.js";import{T as x,a as h,b as u,c as j}from"./tabs-CVrXG0tV.js";import{X as p,D as v,g as f,h as N,i as g,c as y,C as w,j as b,S as C,k,d as S,l as T,m as M,n as P,o as D,G as F,f as A}from"./icons-DaMsayX9.js";import{D as L,a as U,b as Y,c as V}from"./dialog-D3B6nCUe.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";import"./api-BK_ENMzF.js";const z=["BMW","Mercedes-Benz","Audi","Volkswagen","Porsche","Ford","Toyota","Honda","Nissan","Hyundai","Kia","Mazda"],B=["Gasoline","Diesel","Electric","Hybrid","Plug-in Hybrid","LPG","CNG"],R=["Manual","Automatic","CVT","Semi-automatic"],H=["Sedan","Hatchback","SUV","Wagon","Coupe","Convertible","Pickup","Van"],I=["New","Used","Certified Pre-owned","Demo"],$=({filters:d,onFiltersChange:m,onClose:o})=>{const[C,k]=s.useState(d),S=(e,s)=>{k(a=>({...a,[e]:s}))},T=(e,s)=>{const a=C[e]||[],l=a.includes(s)?a.filter(e=>e!==s):[...a,s];S(e,l)};return e.jsxs(a,{className:"border-2 border-primary/20",children:[e.jsx(l,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(i,{className:"text-lg",children:"Advanced Filters"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>{k({}),m({})},children:"Clear All"}),e.jsx(r,{variant:"ghost",size:"sm",onClick:o,children:e.jsx(p,{className:"h-4 w-4"})})]})]})}),e.jsxs(t,{children:[e.jsxs(x,{defaultValue:"basic",className:"w-full",children:[e.jsxs(h,{className:"grid w-full grid-cols-4",children:[e.jsx(u,{value:"basic",children:"Basic"}),e.jsx(u,{value:"specs",children:"Specs"}),e.jsx(u,{value:"location",children:"Location"}),e.jsx(u,{value:"advanced",children:"Advanced"})]}),e.jsxs(j,{value:"basic",className:"space-y-6 mt-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Make"}),e.jsx(c,{placeholder:"Any make",value:C.make||"",onChange:e=>S("make",e.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Model"}),e.jsx(c,{placeholder:"Any model",value:C.model||"",onChange:e=>S("model",e.target.value)})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Popular Makes"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:z.map(s=>e.jsx(n,{variant:C.make===s?"default":"outline",className:"cursor-pointer",onClick:()=>S("make",C.make===s?"":s),children:s},s))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(v,{className:"mr-1 h-4 w-4"}),"Price Range (EUR)"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{type:"number",placeholder:"Min price",value:C.minPrice||"",onChange:e=>S("minPrice",parseInt(e.target.value)||void 0)}),e.jsx(c,{type:"number",placeholder:"Max price",value:C.maxPrice||"",onChange:e=>S("maxPrice",parseInt(e.target.value)||void 0)})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(f,{className:"mr-1 h-4 w-4"}),"Year Range"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{type:"number",placeholder:"From year",value:C.minYear||"",onChange:e=>S("minYear",parseInt(e.target.value)||void 0)}),e.jsx(c,{type:"number",placeholder:"To year",value:C.maxYear||"",onChange:e=>S("maxYear",parseInt(e.target.value)||void 0)})]})]})]}),e.jsxs(j,{value:"specs",className:"space-y-6 mt-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(N,{className:"mr-1 h-4 w-4"}),"Maximum Mileage (km)"]}),e.jsx(c,{type:"number",placeholder:"Max mileage",value:C.maxMileage||"",onChange:e=>S("maxMileage",parseInt(e.target.value)||void 0)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(g,{className:"mr-1 h-4 w-4"}),"Fuel Type"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:B.map(s=>e.jsx(n,{variant:(C.fuelType||[]).includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>T("fuelType",s),children:s},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(y,{className:"mr-1 h-4 w-4"}),"Transmission"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:R.map(s=>e.jsx(n,{variant:(C.transmission||[]).includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>T("transmission",s),children:s},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(w,{className:"mr-1 h-4 w-4"}),"Body Type"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:H.map(s=>e.jsx(n,{variant:(C.bodyType||[]).includes(s)?"default":"outline",className:"cursor-pointer",onClick:()=>T("bodyType",s),children:s},s))})]})]}),e.jsxs(j,{value:"location",className:"space-y-6 mt-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium flex items-center",children:[e.jsx(b,{className:"mr-1 h-4 w-4"}),"Location"]}),e.jsx(c,{placeholder:"City, region, or postal code",value:C.location||"",onChange:e=>S("location",e.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Search Radius (km)"}),e.jsxs("select",{value:C.radius||"",onChange:e=>S("radius",parseInt(e.target.value)||void 0),className:"w-full border rounded px-3 py-2",children:[e.jsx("option",{value:"",children:"Any distance"}),e.jsx("option",{value:"25",children:"25 km"}),e.jsx("option",{value:"50",children:"50 km"}),e.jsx("option",{value:"100",children:"100 km"}),e.jsx("option",{value:"200",children:"200 km"}),e.jsx("option",{value:"500",children:"500 km"})]})]})]}),e.jsx(j,{value:"advanced",className:"space-y-6 mt-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Condition"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:I.map(s=>e.jsx(n,{variant:C.condition===s?"default":"outline",className:"cursor-pointer",onClick:()=>S("condition",C.condition===s?"":s),children:s},s))})]})})]}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-6 pt-4 border-t",children:[e.jsx(r,{variant:"outline",onClick:o,children:"Cancel"}),e.jsx(r,{onClick:()=>{m(C),o()},className:"auto-scouter-gradient",children:"Apply Filters"})]})]})]})},W=[{id:"1",name:"BMW 3 Series Under €25k",searchTerm:"BMW 3 Series",filters:{make:"BMW",model:"3 Series",maxPrice:25e3},resultsCount:47,createdAt:new Date(Date.now()-6048e5),lastUsed:new Date(Date.now()-72e5),isFavorite:!0},{id:"2",name:"Diesel Cars in Munich",searchTerm:"",filters:{fuelType:["Diesel"],location:"Munich",radius:50},resultsCount:156,createdAt:new Date(Date.now()-12096e5),lastUsed:new Date(Date.now()-864e5),isFavorite:!1},{id:"3",name:"Electric Vehicles 2020+",searchTerm:"",filters:{fuelType:["Electric"],minYear:2020},resultsCount:89,createdAt:new Date(Date.now()-18144e5),lastUsed:new Date(Date.now()-2592e5),isFavorite:!0},{id:"4",name:"Luxury SUVs",searchTerm:"",filters:{bodyType:["SUV"],make:["BMW","Mercedes-Benz","Audi"],minPrice:4e4},resultsCount:23,createdAt:new Date(Date.now()-2592e6),lastUsed:new Date(Date.now()-6048e5),isFavorite:!1}],_=({isOpen:l,onClose:i,onLoadSearch:c})=>{const[m,o]=s.useState(W),x=e=>{o(s=>s.map(s=>s.id===e?{...s,isFavorite:!s.isFavorite}:s))},h=e=>{o(s=>s.filter(s=>s.id!==e))},u=e=>{o(s=>s.map(s=>s.id===e.id?{...s,lastUsed:new Date}:s)),c(e)},j=e=>{const s=[];if(e.make&&s.push(e.make),e.model&&s.push(e.model),e.minPrice||e.maxPrice){const a=`€${e.minPrice||0}${e.maxPrice?` - €${e.maxPrice}`:"+"}`;s.push(a)}if(e.minYear||e.maxYear){const a=`${e.minYear||""}${e.maxYear?` - ${e.maxYear}`:"+"}`;s.push(a)}return e.fuelType?.length&&s.push(e.fuelType.join(", ")),e.location&&s.push(`📍 ${e.location}`),s.slice(0,3).join(" • ")+(s.length>3?"...":"")},p=m.filter(e=>e.isFavorite),v=m.filter(e=>!e.isFavorite).sort((e,s)=>s.lastUsed.getTime()-e.lastUsed.getTime());return e.jsx(L,{open:l,onOpenChange:i,children:e.jsxs(U,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[e.jsx(Y,{children:e.jsxs(V,{className:"flex items-center",children:[e.jsx(C,{className:"mr-2 h-5 w-5"}),"Saved Searches"]})}),e.jsxs("div",{className:"space-y-6",children:[p.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center",children:[e.jsx(k,{className:"mr-2 h-4 w-4 text-yellow-500"}),"Favorites"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(s=>e.jsx(a,{className:"card-hover cursor-pointer",children:e.jsxs(t,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("h4",{className:"font-medium truncate",children:s.name}),e.jsx(n,{variant:"secondary",className:"text-xs",children:s.resultsCount})]}),s.searchTerm&&e.jsxs("p",{className:"text-sm text-muted-foreground mb-1",children:['"',s.searchTerm,'"']}),e.jsx("p",{className:"text-xs text-muted-foreground mb-2 truncate",children:j(s.filters)}),e.jsxs("div",{className:"flex items-center text-xs text-muted-foreground",children:[e.jsx(S,{className:"mr-1 h-3 w-3"}),"Last used ",d(s.lastUsed)]})]}),e.jsxs("div",{className:"flex items-center space-x-1 ml-2",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),x(s.id)},children:e.jsx(k,{className:"h-3 w-3 fill-yellow-500 text-yellow-500"})}),e.jsx(r,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),h(s.id)},children:e.jsx(T,{className:"h-3 w-3 text-red-500"})})]})]}),e.jsx(r,{className:"w-full mt-3",onClick:()=>u(s),children:"Load Search"})]})},s.id))})]}),v.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center",children:[e.jsx(S,{className:"mr-2 h-4 w-4"}),"Recent Searches"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.map(s=>e.jsx(a,{className:"card-hover cursor-pointer",children:e.jsxs(t,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("h4",{className:"font-medium truncate",children:s.name}),e.jsx(n,{variant:"secondary",className:"text-xs",children:s.resultsCount})]}),s.searchTerm&&e.jsxs("p",{className:"text-sm text-muted-foreground mb-1",children:['"',s.searchTerm,'"']}),e.jsx("p",{className:"text-xs text-muted-foreground mb-2 truncate",children:j(s.filters)}),e.jsxs("div",{className:"flex items-center text-xs text-muted-foreground",children:[e.jsx(S,{className:"mr-1 h-3 w-3"}),"Last used ",d(s.lastUsed)]})]}),e.jsxs("div",{className:"flex items-center space-x-1 ml-2",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),x(s.id)},children:e.jsx(k,{className:"h-3 w-3"})}),e.jsx(r,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),h(s.id)},children:e.jsx(T,{className:"h-3 w-3 text-red-500"})})]})]}),e.jsx(r,{variant:"outline",className:"w-full mt-3",onClick:()=>u(s),children:"Load Search"})]})},s.id))})]}),0===m.length&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(C,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No saved searches"}),e.jsx("p",{className:"text-muted-foreground",children:"Save your searches to quickly access them later"})]})]})]})})},E=()=>{const[d,x]=s.useState(""),[h,u]=s.useState(!1),[j,p]=s.useState(!1),[v,f]=s.useState("grid"),[N,g]=s.useState("relevance"),[y,w]=s.useState({}),{data:b,isLoading:k,error:S}=m({make:y.make,model:y.model,priceMin:y.minPrice,priceMax:y.maxPrice,yearMin:y.minYear,yearMax:y.maxYear,maxMileage:y.maxMileage,fuelType:y.fuelType?.[0],transmission:y.transmission?.[0],bodyType:y.bodyType?.[0],sort:N,limit:50}),T=b?.total||0,L=Object.values(y).filter(e=>null!=e&&""!==e&&(!Array.isArray(e)||e.length>0)).length;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Vehicle Search"}),e.jsx("p",{className:"text-muted-foreground",children:"Search through thousands of vehicles with advanced filtering options."})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(r,{variant:"outline",onClick:()=>p(!0),children:[e.jsx(M,{className:"mr-2 h-4 w-4"}),"Saved Searches"]}),e.jsxs(r,{variant:"outline",onClick:()=>{},children:[e.jsx(P,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),e.jsxs(a,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(i,{children:"Search Vehicles"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[L>0&&e.jsxs(n,{variant:"secondary",children:[L," filter",1!==L?"s":""," active"]}),e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>{w({}),x("")},children:"Clear All"})]})]})}),e.jsxs(t,{className:"space-y-4",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(C,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(c,{placeholder:"Search by make, model, VIN, or keywords...",value:d,onChange:e=>x(e.target.value),className:"pl-10"})]}),e.jsxs(r,{variant:h?"default":"outline",onClick:()=>u(!h),children:[e.jsx(D,{className:"mr-2 h-4 w-4"}),"Advanced Filters",L>0&&e.jsx(n,{variant:"secondary",className:"ml-2",children:L})]}),e.jsxs(r,{variant:"outline",onClick:()=>{},children:[e.jsx(M,{className:"mr-2 h-4 w-4"}),"Save Search"]})]}),h&&e.jsx($,{filters:y,onFiltersChange:e=>{w(s=>({...s,...e}))},onClose:()=>u(!1)})]})]}),e.jsxs(a,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(i,{children:"Search Results"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:k?e.jsxs("span",{className:"flex items-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-primary mr-2"}),"Loading vehicles..."]}):`${T.toLocaleString()} vehicles found`})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("select",{value:N,onChange:e=>g(e.target.value),className:"text-sm border rounded px-3 py-1",children:[e.jsx("option",{value:"relevance",children:"Sort by Relevance"}),e.jsx("option",{value:"price_low",children:"Price: Low to High"}),e.jsx("option",{value:"price_high",children:"Price: High to Low"}),e.jsx("option",{value:"year_new",children:"Year: Newest First"}),e.jsx("option",{value:"year_old",children:"Year: Oldest First"}),e.jsx("option",{value:"mileage_low",children:"Mileage: Low to High"}),e.jsx("option",{value:"date_added",children:"Recently Added"})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(r,{variant:"grid"===v?"default":"outline",size:"sm",onClick:()=>f("grid"),children:e.jsx(F,{className:"h-4 w-4"})}),e.jsx(r,{variant:"list"===v?"default":"outline",size:"sm",onClick:()=>f("list"),children:e.jsx(A,{className:"h-4 w-4"})})]})]})]})}),e.jsx(t,{children:S?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-red-600 mb-2",children:"Failed to load vehicles"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Please try again later"}),e.jsx(r,{variant:"outline",onClick:()=>window.location.reload(),children:"Retry"})]})}):e.jsx(o,{viewMode:v,searchTerm:d,filters:y})})]}),j&&e.jsx(_,{isOpen:j,onClose:()=>p(!1),onLoadSearch:e=>{x(e.searchTerm||""),w(e.filters||{}),p(!1)}})]})};export{E as VehicleSearch};
