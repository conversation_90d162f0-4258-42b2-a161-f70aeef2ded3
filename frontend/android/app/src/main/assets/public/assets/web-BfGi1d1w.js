import{W as e}from"./index-BNatCL7N.js";import"./ui-B-v3Zwus.js";import"./router-B-XVe37S.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";import"./icons-DaMsayX9.js";class t extends e{constructor(){super(),this.handleVisibilityChange=()=>{const e={isActive:!0!==document.hidden};this.notifyListeners("appStateChange",e),document.hidden?this.notifyListeners("pause",null):this.notifyListeners("resume",null)},document.addEventListener("visibilitychange",this.handleVisibilityChange,!1)}exitApp(){throw this.unimplemented("Not implemented on web.")}async getInfo(){throw this.unimplemented("Not implemented on web.")}async getLaunchUrl(){return{url:""}}async getState(){return{isActive:!0!==document.hidden}}async minimizeApp(){throw this.unimplemented("Not implemented on web.")}}export{t as AppWeb};
