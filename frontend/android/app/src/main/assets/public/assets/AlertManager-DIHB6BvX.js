import{j as e}from"./ui-B-v3Zwus.js";import{r as a}from"./router-B-XVe37S.js";import{C as s,b as t,c as i,d as r,I as l,a as n,B as c}from"./index-BNatCL7N.js";import{S as o}from"./switch-DppGFgFi.js";import{D as d,a as m,b as x,c as h}from"./dropdown-menu-Dvsgogy5.js";import{D as u,a as p,b as j,c as g}from"./dialog-D3B6nCUe.js";import{a as v}from"./api-BK_ENMzF.js";import{T as y,a as f,b as N,c as b}from"./tabs-CVrXG0tV.js";import{v as w,m as C,L as k,w as _,x as A,C as S,A as P,P as T,S as E,B as $,y as F,z as M,I as L,l as D}from"./icons-DaMsayX9.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";const Y=["Gasoline","Diesel","Electric","Hybrid","Plug-in Hybrid","LPG","CNG"],R=["Manual","Automatic","CVT","Semi-automatic"],O=["Sedan","Hatchback","SUV","Wagon","Coupe","Convertible","Pickup","Van"],I=({isOpen:d,onClose:m,onSave:x,initialData:h={},mode:v})=>{const[k,_]=a.useState({name:"",description:"",make:"",model:"",minPrice:void 0,maxPrice:void 0,minYear:void 0,maxYear:void 0,maxMileage:void 0,fuelType:[],transmission:[],bodyType:[],city:"",region:"",locationRadius:void 0,minEnginePower:void 0,maxEnginePower:void 0,condition:"",isActive:!0,notificationFrequency:"immediate",maxNotificationsPerDay:5,...h}),[A,S]=a.useState({});a.useEffect(()=>{d&&(_({name:"",description:"",make:"",model:"",minPrice:void 0,maxPrice:void 0,minYear:void 0,maxYear:void 0,maxMileage:void 0,fuelType:[],transmission:[],bodyType:[],city:"",region:"",locationRadius:void 0,minEnginePower:void 0,maxEnginePower:void 0,condition:"",isActive:!0,notificationFrequency:"immediate",maxNotificationsPerDay:5,...h}),S({}))},[d,h]);const P=(e,a)=>{_(s=>({...s,[e]:a})),A[e]&&S(a=>({...a,[e]:""}))},T=(e,a)=>{const s=k[e]||[],t=s.includes(a)?s.filter(e=>e!==a):[...s,a];P(e,t)};return e.jsx(u,{open:d,onOpenChange:m,children:e.jsxs(p,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[e.jsx(j,{children:e.jsx(g,{children:"create"===v?"Create New Alert":"Edit Alert"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(s,{children:[e.jsx(t,{children:e.jsx(i,{className:"text-lg",children:"Basic Information"})}),e.jsxs(r,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Alert Name *"}),e.jsx(l,{placeholder:"e.g., BMW 3 Series Under €25k",value:k.name,onChange:e=>P("name",e.target.value),className:A.name?"border-red-500":""}),A.name&&e.jsx("p",{className:"text-red-500 text-xs mt-1",children:A.name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Description"}),e.jsx(l,{placeholder:"Optional description",value:k.description,onChange:e=>P("description",e.target.value)})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Active"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Enable notifications for this alert"})]}),e.jsx(o,{checked:k.isActive,onCheckedChange:e=>P("isActive",e)})]})]})]}),e.jsxs(s,{children:[e.jsx(t,{children:e.jsx(i,{className:"text-lg",children:"Vehicle Criteria"})}),e.jsx(r,{children:e.jsxs(y,{defaultValue:"basic",className:"w-full",children:[e.jsxs(f,{className:"grid w-full grid-cols-4",children:[e.jsx(N,{value:"basic",children:"Basic"}),e.jsx(N,{value:"specs",children:"Specs"}),e.jsx(N,{value:"location",children:"Location"}),e.jsx(N,{value:"notifications",children:"Notifications"})]}),e.jsxs(b,{value:"basic",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Make"}),e.jsx(l,{placeholder:"Any make",value:k.make,onChange:e=>P("make",e.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Model"}),e.jsx(l,{placeholder:"Any model",value:k.model,onChange:e=>P("model",e.target.value)})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Price Range (EUR)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(l,{type:"number",placeholder:"Min price",value:k.minPrice||"",onChange:e=>P("minPrice",parseInt(e.target.value)||void 0)}),e.jsx(l,{type:"number",placeholder:"Max price",value:k.maxPrice||"",onChange:e=>P("maxPrice",parseInt(e.target.value)||void 0),className:A.maxPrice?"border-red-500":""})]}),A.maxPrice&&e.jsx("p",{className:"text-red-500 text-xs mt-1",children:A.maxPrice})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Year Range"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(l,{type:"number",placeholder:"From year",value:k.minYear||"",onChange:e=>P("minYear",parseInt(e.target.value)||void 0)}),e.jsx(l,{type:"number",placeholder:"To year",value:k.maxYear||"",onChange:e=>P("maxYear",parseInt(e.target.value)||void 0),className:A.maxYear?"border-red-500":""})]}),A.maxYear&&e.jsx("p",{className:"text-red-500 text-xs mt-1",children:A.maxYear})]})]}),e.jsxs(b,{value:"specs",className:"space-y-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Maximum Mileage (km)"}),e.jsx(l,{type:"number",placeholder:"Max mileage",value:k.maxMileage||"",onChange:e=>P("maxMileage",parseInt(e.target.value)||void 0)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Fuel Type"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Y.map(a=>e.jsx(n,{variant:(k.fuelType||[]).includes(a)?"default":"outline",className:"cursor-pointer",onClick:()=>T("fuelType",a),children:a},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Transmission"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:R.map(a=>e.jsx(n,{variant:(k.transmission||[]).includes(a)?"default":"outline",className:"cursor-pointer",onClick:()=>T("transmission",a),children:a},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Body Type"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:O.map(a=>e.jsx(n,{variant:(k.bodyType||[]).includes(a)?"default":"outline",className:"cursor-pointer",onClick:()=>T("bodyType",a),children:a},a))})]})]}),e.jsxs(b,{value:"location",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"City"}),e.jsx(l,{placeholder:"e.g., Munich",value:k.city,onChange:e=>P("city",e.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Region"}),e.jsx(l,{placeholder:"e.g., Bavaria",value:k.region,onChange:e=>P("region",e.target.value)})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Search Radius (km)"}),e.jsxs("select",{value:k.locationRadius||"",onChange:e=>P("locationRadius",parseInt(e.target.value)||void 0),className:"w-full border rounded px-3 py-2",children:[e.jsx("option",{value:"",children:"Any distance"}),e.jsx("option",{value:"25",children:"25 km"}),e.jsx("option",{value:"50",children:"50 km"}),e.jsx("option",{value:"100",children:"100 km"}),e.jsx("option",{value:"200",children:"200 km"}),e.jsx("option",{value:"500",children:"500 km"})]})]})]}),e.jsxs(b,{value:"notifications",className:"space-y-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Notification Frequency"}),e.jsxs("select",{value:k.notificationFrequency,onChange:e=>P("notificationFrequency",e.target.value),className:"w-full border rounded px-3 py-2",children:[e.jsx("option",{value:"immediate",children:"Immediate"}),e.jsx("option",{value:"daily",children:"Daily Summary"}),e.jsx("option",{value:"weekly",children:"Weekly Summary"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Max Notifications Per Day"}),e.jsx(l,{type:"number",min:"1",max:"50",value:k.maxNotificationsPerDay,onChange:e=>P("maxNotificationsPerDay",parseInt(e.target.value)||5)})]})]})]})})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(c,{variant:"outline",onClick:m,children:"Cancel"}),e.jsxs(c,{variant:"outline",onClick:()=>{},children:[e.jsx(w,{className:"mr-2 h-4 w-4"}),"Test Alert"]}),e.jsxs(c,{onClick:()=>{(()=>{const e={};return k.name.trim()||(e.name="Alert name is required"),k.minPrice&&k.maxPrice&&k.minPrice>k.maxPrice&&(e.maxPrice="Max price must be greater than min price"),k.minYear&&k.maxYear&&k.minYear>k.maxYear&&(e.maxYear="Max year must be greater than min year"),k.minEnginePower&&k.maxEnginePower&&k.minEnginePower>k.maxEnginePower&&(e.maxEnginePower="Max power must be greater than min power"),S(e),0===Object.keys(e).length})()&&x(k)},className:"auto-scouter-gradient",children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"create"===v?"Create Alert":"Save Changes"]})]})]})]})})},q=({isOpen:t,onClose:i,alert:l,onTest:o})=>{const[d,m]=a.useState(!1),[x,h]=a.useState(null);return e.jsx(u,{open:t,onOpenChange:i,children:e.jsxs(p,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[e.jsx(j,{children:e.jsxs(g,{className:"flex items-center",children:[e.jsx(w,{className:"mr-2 h-5 w-5"}),"Test Alert: ",l?.name]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(s,{children:e.jsxs(r,{className:"pt-6",children:[e.jsx("h3",{className:"font-medium mb-3",children:"Alert Criteria"}),e.jsx("div",{className:"space-y-2",children:(e=>{const a=[];if(e.make&&a.push(`Make: ${e.make}`),e.model&&a.push(`Model: ${e.model}`),e.min_price||e.max_price){const s=`Price: €${e.min_price||0} - €${e.max_price||"∞"}`;a.push(s)}if(e.min_year||e.max_year){const s=`Year: ${e.min_year||"∞"} - ${e.max_year||(new Date).getFullYear()}`;a.push(s)}return e.max_mileage&&a.push(`Max Mileage: ${e.max_mileage.toLocaleString()} km`),e.fuel_type&&a.push(`Fuel: ${e.fuel_type}`),e.transmission&&a.push(`Transmission: ${e.transmission}`),e.city&&a.push(`Location: ${e.city}`),a})(l).map((a,s)=>e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("div",{className:"w-2 h-2 bg-primary rounded-full mr-2"}),a]},s))}),l?.description&&e.jsxs("div",{className:"mt-3 p-3 bg-muted/50 rounded text-sm",children:[e.jsx("strong",{children:"Description:"})," ",l.description]})]})}),e.jsx("div",{className:"flex justify-center",children:e.jsx(c,{onClick:async()=>{m(!0),h(null);try{const e=Date.now(),a=await o(l.id),s=Date.now()-e;h({success:!0,matchCount:a.matches?.length||0,matches:a.matches||[],executionTime:s})}catch(e){h({success:!1,matchCount:0,matches:[],error:e.message||"Test failed",executionTime:0})}finally{m(!1)}},disabled:d,className:"auto-scouter-gradient",size:"lg",children:d?e.jsxs(e.Fragment,{children:[e.jsx(k,{className:"mr-2 h-4 w-4 animate-spin"}),"Testing Alert..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"mr-2 h-4 w-4"}),"Run Test"]})})}),x&&e.jsx(s,{children:e.jsxs(r,{className:"pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"font-medium flex items-center",children:[x.success?e.jsx(_,{className:"mr-2 h-5 w-5 text-green-500"}):e.jsx(A,{className:"mr-2 h-5 w-5 text-red-500"}),"Test Results"]}),e.jsx(n,{variant:x.success?"success":"destructive",children:x.success?"Success":"Failed"})]}),x.success?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center p-3 bg-green-50 rounded",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:x.matchCount}),e.jsx("div",{className:"text-sm text-green-700",children:"Matches Found"})]}),e.jsxs("div",{className:"text-center p-3 bg-blue-50 rounded",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[x.executionTime,"ms"]}),e.jsx("div",{className:"text-sm text-blue-700",children:"Execution Time"})]})]}),x.matches.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Sample Matches"}),e.jsxs("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:[x.matches.slice(0,5).map((a,s)=>e.jsxs("div",{className:"flex items-center justify-between p-2 border rounded text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("span",{children:[a.make," ",a.model," (",a.year,")"]})]}),e.jsxs("div",{className:"font-medium",children:["€",a.price?.toLocaleString()]})]},s)),x.matches.length>5&&e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["... and ",x.matches.length-5," more matches"]})]})]}),0===x.matchCount&&e.jsxs("div",{className:"text-center py-4 text-muted-foreground",children:[e.jsx(P,{className:"mx-auto h-8 w-8 mb-2"}),e.jsx("p",{children:"No vehicles match your criteria"}),e.jsx("p",{className:"text-sm",children:"Try adjusting your filters to get more results"})]})]}):e.jsxs("div",{className:"text-center py-4",children:[e.jsx(A,{className:"mx-auto h-8 w-8 text-red-500 mb-2"}),e.jsx("p",{className:"text-red-600 font-medium",children:"Test Failed"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:x.error})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(c,{variant:"outline",onClick:i,children:"Close"}),x?.success&&x.matchCount>0&&e.jsx(c,{variant:"outline",children:"View All Matches"})]})]})]})})},B=()=>{const[t,i]=a.useState(""),[y,f]=a.useState(!1),[N,b]=a.useState(null),[C,k]=a.useState(!1),[_,A]=a.useState(!1),[S,P]=a.useState(!1),{alerts:Y,loading:R,error:O,createAlert:B,updateAlert:z,deleteAlert:U,toggleAlert:V,testAlert:G,fetchAlerts:H}=(()=>{const[e,s]=a.useState([]),[t,i]=a.useState(!1),[r,l]=a.useState(null),[n,c]=a.useState({page:1,pageSize:20,totalCount:0,totalPages:0,hasNext:!1,hasPrev:!1}),o=a.useCallback(async(e={})=>{i(!0),l(null);try{const a=new URLSearchParams;void 0!==e.is_active&&a.append("is_active",e.is_active.toString()),e.page&&a.append("page",e.page.toString()),e.pageSize&&a.append("page_size",e.pageSize.toString());const t=await v.get(`/alerts/?${a.toString()}`);s(t.data.alerts),c(t.data.pagination)}catch(a){l(a.response?.data?.detail||"Failed to fetch alerts")}finally{i(!1)}},[]),d=a.useCallback(async e=>{try{return(await v.get(`/alerts/${e}`)).data}catch(a){throw new Error(a.response?.data?.detail||"Failed to fetch alert")}},[]),m=a.useCallback(async e=>{try{const a=(await v.post("/alerts/",e)).data;return s(e=>[a,...e]),a}catch(a){throw new Error(a.response?.data?.detail||"Failed to create alert")}},[]),x=a.useCallback(async(e,a)=>{try{const t=(await v.put(`/alerts/${e}`,a)).data;return s(a=>a.map(a=>a.id===e?t:a)),t}catch(t){throw new Error(t.response?.data?.detail||"Failed to update alert")}},[]),h=a.useCallback(async e=>{try{await v.delete(`/alerts/${e}`),s(a=>a.filter(a=>a.id!==e))}catch(a){throw new Error(a.response?.data?.detail||"Failed to delete alert")}},[]),u=a.useCallback(async e=>{try{const a=await v.post(`/alerts/${e}/toggle`);return s(s=>s.map(s=>s.id===e?{...s,is_active:a.data.is_active}:s)),a.data}catch(a){throw new Error(a.response?.data?.detail||"Failed to toggle alert")}},[]),p=a.useCallback(async(e,a={})=>{try{return(await v.post(`/alerts/${e}/test`,a)).data}catch(s){throw new Error(s.response?.data?.detail||"Failed to test alert")}},[]),j=a.useCallback(async(e,a=30)=>{try{return(await v.get(`/alerts/${e}/stats?days=${a}`)).data}catch(s){throw new Error(s.response?.data?.detail||"Failed to fetch alert stats")}},[]),g=a.useCallback(async e=>{try{const a=await d(e),s={...a,name:`${a.name} (Copy)`,is_active:!1},{id:t,created_at:i,updated_at:r,last_triggered:l,trigger_count:n,...c}=s;return await m(c)}catch(a){throw new Error(a.response?.data?.detail||"Failed to duplicate alert")}},[d,m]),y=a.useCallback(async e=>{try{const a=e.map(e=>v.post(`/alerts/${e}/toggle`).then(a=>({id:e,is_active:a.data.is_active}))),t=await Promise.all(a);return s(e=>e.map(e=>{const a=t.find(a=>a.id===e.id);return a?{...e,is_active:a.is_active}:e})),t}catch(a){throw new Error("Failed to bulk toggle alerts")}},[]),f=a.useCallback(async e=>{try{const a=e.map(e=>v.delete(`/alerts/${e}`));await Promise.all(a),s(a=>a.filter(a=>!e.includes(a.id)))}catch(a){throw new Error("Failed to bulk delete alerts")}},[]),N=a.useCallback(async(e="json")=>{try{const a=await v.get(`/alerts/export?format=${e}`,{responseType:"blob"}),s=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=s,t.setAttribute("download",`alerts.${e}`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(s)}catch(a){throw new Error("Failed to export alerts")}},[]),b=a.useCallback(async(e,a=!1)=>{try{const s=await v.post("/alerts/import",{alerts:e,overwrite_existing:a});return await o(),s.data}catch(s){throw new Error(s.response?.data?.detail||"Failed to import alerts")}},[o]);return{alerts:e,loading:t,error:r,pagination:n,fetchAlerts:o,getAlert:d,createAlert:m,updateAlert:x,deleteAlert:h,toggleAlert:u,testAlert:p,getAlertStats:j,duplicateAlert:g,bulkToggleAlerts:y,bulkDeleteAlerts:f,exportAlerts:N,importAlerts:b}})();a.useEffect(()=>{H({is_active:!!y||void 0})},[y]);const W=Y.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.description?.toLowerCase().includes(t.toLowerCase())||e.make?.toLowerCase().includes(t.toLowerCase())||e.model?.toLowerCase().includes(t.toLowerCase())),J=e=>{const a=[];if(e.make&&a.push(`Make: ${e.make}`),e.model&&a.push(`Model: ${e.model}`),e.min_price||e.max_price){const s=[e.min_price?`€${e.min_price.toLocaleString()}`:"",e.max_price?`€${e.max_price.toLocaleString()}`:""].filter(Boolean).join(" - ");a.push(`Price: ${s}`)}if(e.min_year||e.max_year){const s=[e.min_year,e.max_year].filter(Boolean).join(" - ");a.push(`Year: ${s}`)}return e.max_mileage&&a.push(`Max mileage: ${e.max_mileage.toLocaleString()} km`),e.fuel_type&&a.push(`Fuel: ${e.fuel_type}`),e.transmission&&a.push(`Transmission: ${e.transmission}`),e.city&&a.push(`Location: ${e.city}`),a.slice(0,3).join(", ")+(a.length>3?"...":"")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("header",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Alert Management"}),e.jsx("p",{className:"text-gray-600",children:"Create and manage your vehicle alerts"})]}),e.jsxs(c,{onClick:()=>k(!0),"aria-label":"Create a new vehicle alert",children:[e.jsx(T,{className:"h-4 w-4 mr-2","aria-hidden":"true"}),"Create Alert"]})]}),e.jsx(s,{children:e.jsx(r,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(E,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400","aria-hidden":"true"}),e.jsx(l,{placeholder:"Search alerts...",value:t,onChange:e=>i(e.target.value),className:"pl-10","aria-label":"Search through your vehicle alerts"})]})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{checked:y,onCheckedChange:f,id:"active-only"}),e.jsx("label",{htmlFor:"active-only",className:"text-sm font-medium",children:"Active only"})]})]})})}),R?e.jsx("div",{className:"flex items-center justify-center h-32",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):O?e.jsx(s,{children:e.jsxs(r,{className:"p-8 text-center",children:[e.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load alerts"}),e.jsx(c,{variant:"outline",onClick:()=>H(),children:"Retry"})]})}):0===W.length?e.jsx(s,{children:e.jsxs(r,{className:"p-8 text-center",children:[e.jsx($,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No alerts found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:t?"No alerts match your search criteria.":"Create your first alert to get started."}),!t&&e.jsxs(c,{onClick:()=>k(!0),children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Create Alert"]})]})}):e.jsx("div",{className:"grid gap-4",children:W.map(a=>e.jsx(s,{className:""+(a.is_active?"":"opacity-60"),children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a.name}),e.jsx(n,{variant:a.is_active?"default":"secondary",children:a.is_active?"Active":"Inactive"}),a.trigger_count>0&&e.jsxs(n,{variant:"outline",className:"text-xs",children:[a.trigger_count," matches"]})]}),a.description&&e.jsx("p",{className:"text-gray-600 mb-3",children:a.description}),e.jsxs("div",{className:"text-sm text-gray-500 mb-3",children:[e.jsx("strong",{children:"Criteria:"})," ",J(a)]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[e.jsxs("span",{children:["Created ",new Date(a.created_at).toLocaleDateString()]}),a.last_triggered&&e.jsxs("span",{children:["Last triggered ",new Date(a.last_triggered).toLocaleDateString()]}),e.jsxs("span",{children:["Frequency: ",a.notification_frequency]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(c,{variant:"ghost",size:"sm",onClick:()=>(async e=>{try{await V(e),H()}catch(a){}})(a.id),children:a.is_active?e.jsx(F,{className:"h-4 w-4"}):e.jsx($,{className:"h-4 w-4"})}),e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(c,{variant:"ghost",size:"sm",children:e.jsx(M,{className:"h-4 w-4"})})}),e.jsxs(x,{align:"end",children:[e.jsxs(h,{onClick:()=>{b(a),A(!0)},children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Edit"]}),e.jsxs(h,{onClick:()=>(e=>{b(e),P(!0)})(a),children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Test"]}),e.jsxs(h,{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to delete this alert?"))try{await U(e),H()}catch(a){}})(a.id),className:"text-red-600",children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]})})},a.id))}),e.jsx(u,{open:C,onOpenChange:k,children:e.jsxs(p,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[e.jsx(j,{children:e.jsx(g,{children:"Create New Alert"})}),e.jsx(I,{isOpen:C,onClose:()=>k(!1),onSave:async e=>{try{await B(e),k(!1),H()}catch(a){}},mode:"create"})]})}),e.jsx(u,{open:_,onOpenChange:A,children:e.jsxs(p,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[e.jsx(j,{children:e.jsx(g,{children:"Edit Alert"})}),e.jsx(I,{isOpen:_,onClose:()=>{A(!1),b(null)},onSave:async e=>{if(N)try{await z(N.id,e),A(!1),b(null),H()}catch(a){}},initialData:N?(K=N,{name:K.name,description:K.description,make:K.make,model:K.model,minPrice:K.min_price,maxPrice:K.max_price,minYear:K.min_year,maxYear:K.max_year,maxMileage:K.max_mileage,fuelType:K.fuel_type?[K.fuel_type]:void 0,transmission:K.transmission?[K.transmission]:void 0,bodyType:K.body_type?[K.body_type]:void 0,city:K.city,region:K.region,locationRadius:K.location_radius,minEnginePower:K.min_engine_power,maxEnginePower:K.max_engine_power,condition:K.condition,isActive:K.is_active,notificationFrequency:K.notification_frequency,maxNotificationsPerDay:K.max_notifications_per_day}):void 0,mode:"edit"})]})}),N&&e.jsx(q,{alert:N,isOpen:S,onClose:()=>{P(!1),b(null)},onTest:G})]});var K};export{B as AlertManager,B as default};
