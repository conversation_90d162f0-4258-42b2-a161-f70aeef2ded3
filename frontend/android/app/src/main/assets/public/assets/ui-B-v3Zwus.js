import{r as e,R as t,a as n,b as r,c as o}from"./router-B-XVe37S.js";import{r as i}from"./vendor-DEQ385Nk.js";var a,c,s={exports:{}},u={};var l=(c||(c=1,s.exports=function(){if(a)return u;a=1;var e=i(),t=Symbol.for("react.element"),n=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function s(e,n,i){var a,s={},u=null,l=null;for(a in void 0!==i&&(u=""+i),void 0!==n.key&&(u=""+n.key),void 0!==n.ref&&(l=n.ref),n)r.call(n,a)&&!c.hasOwnProperty(a)&&(s[a]=n[a]);if(e&&e.defaultProps)for(a in n=e.defaultProps)void 0===s[a]&&(s[a]=n[a]);return{$$typeof:t,type:e,key:u,ref:l,props:s,_owner:o.current}}return u.Fragment=n,u.jsx=s,u.jsxs=s,u}()),s.exports);function d(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function f(...e){return t=>{let n=!1;const r=e.map(e=>{const r=d(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():d(e[t],null)}}}}function p(...t){return e.useCallback(f(...t),t)}function m(t){const n=v(t),r=e.forwardRef((t,r)=>{const{children:o,...i}=t,a=e.Children.toArray(o),c=a.find(y);if(c){const t=c.props.children,o=a.map(n=>n===c?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n);return l.jsx(n,{...i,ref:r,children:e.isValidElement(t)?e.cloneElement(t,void 0,o):null})}return l.jsx(n,{...i,ref:r,children:o})});return r.displayName=`${t}.Slot`,r}var h=m("Slot");function v(t){const n=e.forwardRef((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?f(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null});return n.displayName=`${t}.SlotClone`,n}var g=Symbol("radix.slottable");function y(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===g}function w(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function b(t,n=[]){let r=[];const o=()=>{const n=r.map(t=>e.createContext(t));return function(r){const o=r?.[t]||n;return e.useMemo(()=>({[`__scope${t}`]:{...r,[t]:o}}),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;r=[...r,o];const c=n=>{const{scope:r,children:o,...c}=n,s=r?.[t]?.[a]||i,u=e.useMemo(()=>c,Object.values(c));return l.jsx(s.Provider,{value:u,children:o})};return c.displayName=n+"Provider",[c,function(r,c){const s=c?.[t]?.[a]||i,u=e.useContext(s);if(u)return u;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},x(o,...n)]}function x(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const o=r.reduce((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}var E=globalThis?.document?e.useLayoutEffect:()=>{},R=t[" useInsertionEffect ".trim().toString()]||E;function C({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,a,c]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),a=e.useRef(n);return R(()=>{a.current=n},[n]),e.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,o,a]}({defaultProp:n,onChange:r}),s=void 0!==t,u=s?t:i;{const n=e.useRef(void 0!==t);e.useEffect(()=>{const e=n.current;if(e!==s){}n.current=s},[s,o])}const l=e.useCallback(e=>{if(s){const n=function(e){return"function"==typeof e}(e)?e(t):e;n!==t&&c.current?.(n)}else a(e)},[s,t,a,c]);return[u,l]}function D(t){const[n,r]=e.useState(void 0);return E(()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver(e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)},[t]),n}var _=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,n)=>{const r=m(`Primitive.${n}`),o=e.forwardRef((e,t)=>{const{asChild:o,...i}=e,a=o?r:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),l.jsx(a,{...i,ref:t})});return o.displayName=`Primitive.${n}`,{...t,[n]:o}},{});function M(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}function S(e){const t=e+"CollectionProvider",[n,o]=b(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{const{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return l.jsx(i,{scope:t,itemMap:a,collectionRef:o,children:n})};c.displayName=t;const s=e+"CollectionSlot",u=m(s),d=r.forwardRef((e,t)=>{const{scope:n,children:r}=e,o=p(t,a(s,n).collectionRef);return l.jsx(u,{ref:o,children:r})});d.displayName=s;const f=e+"CollectionItemSlot",h="data-radix-collection-item",v=m(f),g=r.forwardRef((e,t)=>{const{scope:n,children:o,...i}=e,c=r.useRef(null),s=p(t,c),u=a(f,n);return r.useEffect(()=>(u.itemMap.set(c,{ref:c,...i}),()=>{u.itemMap.delete(c)})),l.jsx(v,{[h]:"",ref:s,children:o})});return g.displayName=f,[{Provider:c,Slot:d,ItemSlot:g},function(t){const n=a(e+"CollectionConsumer",t);return r.useCallback(()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}var P=t[" useId ".trim().toString()]||(()=>{}),O=0;function T(t){const[n,r]=e.useState(P());return E(()=>{r(e=>e??String(O++))},[t]),t||(n?`radix-${n}`:"")}function A(t){const n=e.useRef(t);return e.useEffect(()=>{n.current=t}),e.useMemo(()=>(...e)=>n.current?.(...e),[])}var j=e.createContext(void 0);function N(t){const n=e.useContext(j);return t||n||"ltr"}var k="rovingFocusGroup.onEntryFocus",I={bubbles:!1,cancelable:!0},L="RovingFocusGroup",[F,W,B]=S(L),[K,$]=b(L,[B]),[H,V]=K(L),U=e.forwardRef((e,t)=>l.jsx(F.Provider,{scope:e.__scopeRovingFocusGroup,children:l.jsx(F.Slot,{scope:e.__scopeRovingFocusGroup,children:l.jsx(z,{...e,ref:t})})}));U.displayName=L;var z=e.forwardRef((t,n)=>{const{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:a,currentTabStopId:c,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:u,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...m}=t,h=e.useRef(null),v=p(n,h),g=N(a),[y,b]=C({prop:c,defaultProp:s??null,onChange:u,caller:L}),[x,E]=e.useState(!1),R=A(d),D=W(r),M=e.useRef(!1),[S,P]=e.useState(0);return e.useEffect(()=>{const e=h.current;if(e)return e.addEventListener(k,R),()=>e.removeEventListener(k,R)},[R]),l.jsx(H,{scope:r,orientation:o,dir:g,loop:i,currentTabStopId:y,onItemFocus:e.useCallback(e=>b(e),[b]),onItemShiftTab:e.useCallback(()=>E(!0),[]),onFocusableItemAdd:e.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:e.useCallback(()=>P(e=>e-1),[]),children:l.jsx(_.div,{tabIndex:x||0===S?-1:0,"data-orientation":o,...m,ref:v,style:{outline:"none",...t.style},onMouseDown:w(t.onMouseDown,()=>{M.current=!0}),onFocus:w(t.onFocus,e=>{const t=!M.current;if(e.target===e.currentTarget&&t&&!x){const t=new CustomEvent(k,I);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=D().filter(e=>e.focusable);q([e.find(e=>e.active),e.find(e=>e.id===y),...e].filter(Boolean).map(e=>e.ref.current),f)}}M.current=!1}),onBlur:w(t.onBlur,()=>E(!1))})})}),G="RovingFocusGroupItem",X=e.forwardRef((t,n)=>{const{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:a,children:c,...s}=t,u=T(),d=a||u,f=V(G,r),p=f.currentTabStopId===d,m=W(r),{onFocusableItemAdd:h,onFocusableItemRemove:v,currentTabStopId:g}=f;return e.useEffect(()=>{if(o)return h(),()=>v()},[o,h,v]),l.jsx(F.ItemSlot,{scope:r,id:d,focusable:o,active:i,children:l.jsx(_.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...s,ref:n,onMouseDown:w(t.onMouseDown,e=>{o?f.onItemFocus(d):e.preventDefault()}),onFocus:w(t.onFocus,()=>f.onItemFocus(d)),onKeyDown:w(t.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:Y[r]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const i=o.indexOf(e.currentTarget);o=f.loop?(r=i+1,(n=o).map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>q(o))}var n,r}),children:"function"==typeof c?c({isCurrentTabStop:p,hasTabStop:null!=g}):c})})});X.displayName=G;var Y={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function q(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var Z=U,J=X;var Q=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),a=e.useRef("none"),c=t?"mounted":"unmounted",[s,u]=function(t,n){return e.useReducer((e,t)=>n[e][t]??e,t)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=ee(o.current);a.current="mounted"===s?e:"none"},[s]),E(()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=ee(e);if(t)u("MOUNT");else if("none"===o||"none"===e?.display)u("UNMOUNT");else{u(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}},[t,u]),E(()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=ee(o.current).includes(r.animationName);if(r.target===n&&a&&(u("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},c=e=>{e.target===n&&(a.current=ee(o.current))};return n.addEventListener("animationstart",c),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",c),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:e.useCallback(e=>{o.current=e?getComputedStyle(e):null,r(e)},[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=p(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function ee(e){return e?.animationName||"none"}Q.displayName="Presence";var te="Tabs",[ne,re]=b(te,[$]),oe=$(),[ie,ae]=ne(te),ce=e.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:c,activationMode:s="automatic",...u}=e,d=N(c),[f,p]=C({prop:r,onChange:o,defaultProp:i??"",caller:te});return l.jsx(ie,{scope:n,baseId:T(),value:f,onValueChange:p,orientation:a,dir:d,activationMode:s,children:l.jsx(_.div,{dir:d,"data-orientation":a,...u,ref:t})})});ce.displayName=te;var se="TabsList",ue=e.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,i=ae(se,n),a=oe(n);return l.jsx(Z,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:l.jsx(_.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});ue.displayName=se;var le="TabsTrigger",de=e.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,a=ae(le,n),c=oe(n),s=me(a.baseId,r),u=he(a.baseId,r),d=r===a.value;return l.jsx(J,{asChild:!0,...c,focusable:!o,active:d,children:l.jsx(_.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:s,...i,ref:t,onMouseDown:w(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(r)}),onKeyDown:w(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(r)}),onFocus:w(e.onFocus,()=>{const e="manual"!==a.activationMode;d||o||!e||a.onValueChange(r)})})})});de.displayName=le;var fe="TabsContent",pe=e.forwardRef((t,n)=>{const{__scopeTabs:r,value:o,forceMount:i,children:a,...c}=t,s=ae(fe,r),u=me(s.baseId,o),d=he(s.baseId,o),f=o===s.value,p=e.useRef(f);return e.useEffect(()=>{const e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),l.jsx(Q,{present:i||f,children:({present:e})=>l.jsx(_.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!e,id:d,tabIndex:0,...c,ref:n,style:{...t.style,animationDuration:p.current?"0s":void 0},children:e&&a})})});function me(e,t){return`${e}-trigger-${t}`}function he(e,t){return`${e}-content-${t}`}pe.displayName=fe;var ve=ce,ge=ue,ye=de,we=pe;var be,xe="dismissableLayer.update",Ee="dismissableLayer.pointerDownOutside",Re="dismissableLayer.focusOutside",Ce=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),De=e.forwardRef((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:c,onDismiss:s,...u}=t,d=e.useContext(Ce),[f,m]=e.useState(null),h=f?.ownerDocument??globalThis?.document,[,v]=e.useState({}),g=p(n,e=>m(e)),y=Array.from(d.layers),[b]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),x=y.indexOf(b),E=f?y.indexOf(f):-1,R=d.layersWithOutsidePointerEventsDisabled.size>0,C=E>=x,D=function(t,n=globalThis?.document){const r=A(t),o=e.useRef(!1),i=e.useRef(()=>{});return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){let t=function(){Me(Ee,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{const t=e.target,n=[...d.branches].some(e=>e.contains(t));C&&!n&&(i?.(e),c?.(e),e.defaultPrevented||s?.())},h),M=function(t,n=globalThis?.document){const r=A(t),o=e.useRef(!1);return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){Me(Re,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{const t=e.target;[...d.branches].some(e=>e.contains(t))||(a?.(e),c?.(e),e.defaultPrevented||s?.())},h);return function(t,n=globalThis?.document){const r=A(t);e.useEffect(()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})},[r,n])}(e=>{E===d.layers.size-1&&(o?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},h),e.useEffect(()=>{if(f)return r&&(0===d.layersWithOutsidePointerEventsDisabled.size&&(be=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),_e(),()=>{r&&1===d.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=be)}},[f,h,r,d]),e.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),_e())},[f,d]),e.useEffect(()=>{const e=()=>v({});return document.addEventListener(xe,e),()=>document.removeEventListener(xe,e)},[]),l.jsx(_.div,{...u,ref:g,style:{pointerEvents:R?C?"auto":"none":void 0,...t.style},onFocusCapture:w(t.onFocusCapture,M.onFocusCapture),onBlurCapture:w(t.onBlurCapture,M.onBlurCapture),onPointerDownCapture:w(t.onPointerDownCapture,D.onPointerDownCapture)})});De.displayName="DismissableLayer";function _e(){const e=new CustomEvent(xe);document.dispatchEvent(e)}function Me(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?M(o,i):o.dispatchEvent(i)}e.forwardRef((t,n)=>{const r=e.useContext(Ce),o=e.useRef(null),i=p(n,o);return e.useEffect(()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),l.jsx(_.div,{...t,ref:i})}).displayName="DismissableLayerBranch";var Se="focusScope.autoFocusOnMount",Pe="focusScope.autoFocusOnUnmount",Oe={bubbles:!1,cancelable:!0},Te=e.forwardRef((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...c}=t,[s,u]=e.useState(null),d=A(i),f=A(a),m=e.useRef(null),h=p(n,e=>u(e)),v=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect(()=>{if(o){let e=function(e){if(v.paused||!s)return;const t=e.target;s.contains(t)?m.current=t:ke(m.current,{select:!0})},t=function(e){if(v.paused||!s)return;const t=e.relatedTarget;null!==t&&(s.contains(t)||ke(m.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&ke(s)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[o,s,v.paused]),e.useEffect(()=>{if(s){Ie.add(v);const t=document.activeElement;if(!s.contains(t)){const n=new CustomEvent(Se,Oe);s.addEventListener(Se,d),s.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ke(r,{select:t}),document.activeElement!==n)return}((e=Ae(s),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===t&&ke(s))}return()=>{s.removeEventListener(Se,d),setTimeout(()=>{const e=new CustomEvent(Pe,Oe);s.addEventListener(Pe,f),s.dispatchEvent(e),e.defaultPrevented||ke(t??document.body,{select:!0}),s.removeEventListener(Pe,f),Ie.remove(v)},0)}}var e},[s,d,f,v]);const g=e.useCallback(e=>{if(!r&&!o)return;if(v.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,i]=function(e){const t=Ae(e),n=je(t,e),r=je(t.reverse(),e);return[n,r]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&ke(i,{select:!0})):(e.preventDefault(),r&&ke(o,{select:!0})):n===t&&e.preventDefault()}},[r,o,v.paused]);return l.jsx(_.div,{tabIndex:-1,...c,ref:h,onKeyDown:g})});function Ae(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function je(e,t){for(const n of e)if(!Ne(n,{upTo:t}))return n}function Ne(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function ke(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}Te.displayName="FocusScope";var Ie=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Le(e,t),e.unshift(t)},remove(t){e=Le(e,t),e[0]?.resume()}}}();function Le(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var Fe=e.forwardRef((t,n)=>{const{container:r,...i}=t,[a,c]=e.useState(!1);E(()=>c(!0),[]);const s=r||a&&globalThis?.document?.body;return s?o.createPortal(l.jsx(_.div,{...i,ref:n}),s):null});Fe.displayName="Portal";var We=0;function Be(){e.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ke()),document.body.insertAdjacentElement("beforeend",e[1]??Ke()),We++,()=>{1===We&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),We--}},[])}function Ke(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var $e=function(){return $e=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},$e.apply(this,arguments)};function He(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var Ve="right-scroll-bar-position",Ue="width-before-scroll-bar";function ze(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var Ge="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,Xe=new WeakMap;function Ye(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach(function(t){return ze(t,e)})},(i=e.useState(function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=o,i.facade);return Ge(function(){var e=Xe.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach(function(e){r.has(e)||ze(e,null)}),r.forEach(function(e){n.has(e)||ze(e,o)})}Xe.set(a,t)},[t]),a}function qe(e){return e}var Ze=function(t){var n=t.sideCar,r=He(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,$e({},r))};Ze.isSideCarExport=!0;var Je=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=qe);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=$e({async:!0,ssr:!1},e),t}(),Qe=function(){},et=e.forwardRef(function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:Qe,onWheelCapture:Qe,onTouchMoveCapture:Qe}),i=o[0],a=o[1],c=t.forwardProps,s=t.children,u=t.className,l=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noRelative,h=t.noIsolation,v=t.inert,g=t.allowPinchZoom,y=t.as,w=void 0===y?"div":y,b=t.gapMode,x=He(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=p,R=Ye([r,n]),C=$e($e({},x),i);return e.createElement(e.Fragment,null,d&&e.createElement(E,{sideCar:Je,removeScrollBar:l,shards:f,noRelative:m,noIsolation:h,inert:v,setCallbacks:a,allowPinchZoom:!!g,lockRef:r,gapMode:b}),c?e.cloneElement(e.Children.only(s),$e($e({},C),{ref:R})):e.createElement(w,$e({},C,{className:u,ref:R}),s))});et.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},et.classNames={fullWidth:Ue,zeroRight:Ve};function tt(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var nt=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=tt())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rt=function(){var t,n=(t=nt(),function(n,r){e.useEffect(function(){return t.add(n),function(){t.remove()}},[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},ot={left:0,top:0,right:0,gap:0},it=function(e){return parseInt(e||"",10)||0},at=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ot;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[it(n),it(r),it(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ct=rt(),st="data-scroll-locked",ut=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(st,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(Ve," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(Ue," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(Ve," .").concat(Ve," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(Ue," .").concat(Ue," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(st,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},lt=function(){var e=parseInt(document.body.getAttribute(st)||"0",10);return isFinite(e)?e:0},dt=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect(function(){return document.body.setAttribute(st,(lt()+1).toString()),function(){var e=lt()-1;e<=0?document.body.removeAttribute(st):document.body.setAttribute(st,e.toString())}},[]);var a=e.useMemo(function(){return at(i)},[i]);return e.createElement(ct,{styles:ut(a,!n,i,r?"":"!important")})},ft=!1;if("undefined"!=typeof window)try{var pt=Object.defineProperty({},"passive",{get:function(){return ft=!0,!0}});window.addEventListener("test",pt,pt),window.removeEventListener("test",pt,pt)}catch(ba){ft=!1}var mt=!!ft&&{passive:!1},ht=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},vt=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),gt(e,r)){var o=yt(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},gt=function(e,t){return"v"===e?function(e){return ht(e,"overflowY")}(t):function(e){return ht(e,"overflowX")}(t)},yt=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},wt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},bt=function(e){return[e.deltaX,e.deltaY]},xt=function(e){return e&&"current"in e?e.current:e},Et=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Rt=0,Ct=[];function Dt(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const _t=(Mt=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(Rt++)[0],a=e.useState(rt)[0],c=e.useRef(t);e.useEffect(function(){c.current=t},[t]),e.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(xt),!0).filter(Boolean);return e.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[t.inert,t.lockRef.current,t.shards]);var s=e.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var n,i=wt(e),a=r.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],l=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===l.type)return!1;var f=vt(d,l);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=vt(d,l)),!f)return!1;if(!o.current&&"changedTouches"in e&&(s||u)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,c=t.contains(a),s=!1,u=i>0,l=0,d=0;do{if(!a)break;var f=yt(e,a),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&gt(e,a)&&(l+=m,d+=p);var h=a.parentNode;a=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(u&&Math.abs(l)<1||!u&&Math.abs(d)<1)&&(s=!0),s}(p,t,e,"h"===p?s:u)},[]),u=e.useCallback(function(e){var t=e;if(Ct.length&&Ct[Ct.length-1]===a){var r="deltaY"in t?bt(t):wt(t),o=n.current.filter(function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o})[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(c.current.shards||[]).map(xt).filter(Boolean).filter(function(e){return e.contains(t.target)});(i.length>0?s(t,i[0]):!c.current.noIsolation)&&t.cancelable&&t.preventDefault()}}},[]),l=e.useCallback(function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:Dt(r)};n.current.push(i),setTimeout(function(){n.current=n.current.filter(function(e){return e!==i})},1)},[]),d=e.useCallback(function(e){r.current=wt(e),o.current=void 0},[]),f=e.useCallback(function(e){l(e.type,bt(e),e.target,s(e,t.lockRef.current))},[]),p=e.useCallback(function(e){l(e.type,wt(e),e.target,s(e,t.lockRef.current))},[]);e.useEffect(function(){return Ct.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,mt),document.addEventListener("touchmove",u,mt),document.addEventListener("touchstart",d,mt),function(){Ct=Ct.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,mt),document.removeEventListener("touchmove",u,mt),document.removeEventListener("touchstart",d,mt)}},[]);var m=t.removeScrollBar,h=t.inert;return e.createElement(e.Fragment,null,h?e.createElement(a,{styles:Et(i)}):null,m?e.createElement(dt,{noRelative:t.noRelative,gapMode:t.gapMode}):null)},Je.useMedium(Mt),Ze);var Mt,St=e.forwardRef(function(t,n){return e.createElement(et,$e({},t,{ref:n,sideCar:_t}))});St.classNames=et.classNames;var Pt=new WeakMap,Ot=new WeakMap,Tt={},At=0,jt=function(e){return e&&(e.host||jt(e.parentNode))},Nt=function(e,t,n,r){var o=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=jt(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);Tt[n]||(Tt[n]=new WeakMap);var i=Tt[n],a=[],c=new Set,s=new Set(o),u=function(e){e&&!c.has(e)&&(c.add(e),u(e.parentNode))};o.forEach(u);var l=function(e){e&&!s.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(c.has(e))l(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(Pt.get(e)||0)+1,u=(i.get(e)||0)+1;Pt.set(e,s),i.set(e,u),a.push(e),1===s&&o&&Ot.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}})};return l(t),c.clear(),At++,function(){a.forEach(function(e){var t=Pt.get(e)-1,o=i.get(e)-1;Pt.set(e,t),i.set(e,o),t||(Ot.has(e)||e.removeAttribute(r),Ot.delete(e)),o||e.removeAttribute(n)}),--At||(Pt=new WeakMap,Pt=new WeakMap,Ot=new WeakMap,Tt={})}},kt=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Nt(r,o,n,"aria-hidden")):function(){return null}},It="Dialog",[Lt,Ft]=b(It),[Wt,Bt]=Lt(It),Kt=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:c=!0}=t,s=e.useRef(null),u=e.useRef(null),[d,f]=C({prop:o,defaultProp:i??!1,onChange:a,caller:It});return l.jsx(Wt,{scope:n,triggerRef:s,contentRef:u,contentId:T(),titleId:T(),descriptionId:T(),open:d,onOpenChange:f,onOpenToggle:e.useCallback(()=>f(e=>!e),[f]),modal:c,children:r})};Kt.displayName=It;var $t="DialogTrigger";e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Bt($t,n),i=p(t,o.triggerRef);return l.jsx(_.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":un(o.open),...r,ref:i,onClick:w(e.onClick,o.onOpenToggle)})}).displayName=$t;var Ht="DialogPortal",[Vt,Ut]=Lt(Ht,{forceMount:void 0}),zt=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:i}=t,a=Bt(Ht,n);return l.jsx(Vt,{scope:n,forceMount:r,children:e.Children.map(o,e=>l.jsx(Q,{present:r||a.open,children:l.jsx(Fe,{asChild:!0,container:i,children:e})}))})};zt.displayName=Ht;var Gt="DialogOverlay",Xt=e.forwardRef((e,t)=>{const n=Ut(Gt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Bt(Gt,e.__scopeDialog);return i.modal?l.jsx(Q,{present:r||i.open,children:l.jsx(qt,{...o,ref:t})}):null});Xt.displayName=Gt;var Yt=m("DialogOverlay.RemoveScroll"),qt=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Bt(Gt,n);return l.jsx(St,{as:Yt,allowPinchZoom:!0,shards:[o.contentRef],children:l.jsx(_.div,{"data-state":un(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Zt="DialogContent",Jt=e.forwardRef((e,t)=>{const n=Ut(Zt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Bt(Zt,e.__scopeDialog);return l.jsx(Q,{present:r||i.open,children:i.modal?l.jsx(Qt,{...o,ref:t}):l.jsx(en,{...o,ref:t})})});Jt.displayName=Zt;var Qt=e.forwardRef((t,n)=>{const r=Bt(Zt,t.__scopeDialog),o=e.useRef(null),i=p(n,r.contentRef,o);return e.useEffect(()=>{const e=o.current;if(e)return kt(e)},[]),l.jsx(tn,{...t,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:w(t.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:w(t.onPointerDownOutside,e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:w(t.onFocusOutside,e=>e.preventDefault())})}),en=e.forwardRef((t,n)=>{const r=Bt(Zt,t.__scopeDialog),o=e.useRef(!1),i=e.useRef(!1);return l.jsx(tn,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(o.current||r.triggerRef.current?.focus(),e.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));const n=e.target,a=r.triggerRef.current?.contains(n);a&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})}),tn=e.forwardRef((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...c}=t,s=Bt(Zt,r),u=e.useRef(null),d=p(n,u);return Be(),l.jsxs(l.Fragment,{children:[l.jsx(Te,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:l.jsx(De,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":un(s.open),...c,ref:d,onDismiss:()=>s.onOpenChange(!1)})}),l.jsxs(l.Fragment,{children:[l.jsx(pn,{titleId:s.titleId}),l.jsx(mn,{contentRef:u,descriptionId:s.descriptionId})]})]})}),nn="DialogTitle",rn=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Bt(nn,n);return l.jsx(_.h2,{id:o.titleId,...r,ref:t})});rn.displayName=nn;var on="DialogDescription",an=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Bt(on,n);return l.jsx(_.p,{id:o.descriptionId,...r,ref:t})});an.displayName=on;var cn="DialogClose",sn=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Bt(cn,n);return l.jsx(_.button,{type:"button",...r,ref:t,onClick:w(e.onClick,()=>o.onOpenChange(!1))})});function un(e){return e?"open":"closed"}sn.displayName=cn;var ln="DialogTitleWarning",[dn,fn]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,i=e.useMemo(()=>o,Object.values(o));return l.jsx(r.Provider,{value:i,children:n})};return o.displayName=t+"Provider",[o,function(o){const i=e.useContext(r);if(i)return i;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(ln,{contentName:Zt,titleName:nn,docsSlug:"dialog"}),pn=({titleId:t})=>{const n=fn(ln),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect(()=>{if(t){document.getElementById(t)}},[r,t]),null},mn=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${fn("DialogDescriptionWarning").contentName}}.`;return e.useEffect(()=>{const e=t.current?.getAttribute("aria-describedby");if(n&&e){document.getElementById(n)}},[r,t,n]),null},hn=Kt,vn=zt,gn=Xt,yn=Jt,wn=rn,bn=an,xn=sn;const En=["top","right","bottom","left"],Rn=Math.min,Cn=Math.max,Dn=Math.round,_n=Math.floor,Mn=e=>({x:e,y:e}),Sn={left:"right",right:"left",bottom:"top",top:"bottom"},Pn={start:"end",end:"start"};function On(e,t,n){return Cn(e,Rn(t,n))}function Tn(e,t){return"function"==typeof e?e(t):e}function An(e){return e.split("-")[0]}function jn(e){return e.split("-")[1]}function Nn(e){return"x"===e?"y":"x"}function kn(e){return"y"===e?"height":"width"}function In(e){return["top","bottom"].includes(An(e))?"y":"x"}function Ln(e){return Nn(In(e))}function Fn(e){return e.replace(/start|end/g,e=>Pn[e])}function Wn(e){return e.replace(/left|right|bottom|top/g,e=>Sn[e])}function Bn(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function Kn(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function $n(e,t,n){let{reference:r,floating:o}=e;const i=In(t),a=Ln(t),c=kn(a),s=An(t),u="y"===i,l=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[c]/2-o[c]/2;let p;switch(s){case"top":p={x:l,y:r.y-o.height};break;case"bottom":p={x:l,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(jn(t)){case"start":p[a]-=f*(n&&u?-1:1);break;case"end":p[a]+=f*(n&&u?-1:1)}return p}async function Hn(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:c,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:l="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Tn(t,e),m=Bn(p),h=c[f?"floating"===d?"reference":"floating":d],v=Kn(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(c.floating)),boundary:u,rootBoundary:l,strategy:s})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=Kn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-b.top+m.top)/w.y,bottom:(b.bottom-v.bottom+m.bottom)/w.y,left:(v.left-b.left+m.left)/w.x,right:(b.right-v.right+m.right)/w.x}}function Vn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Un(e){return En.some(t=>e[t]>=0)}function zn(){return"undefined"!=typeof window}function Gn(e){return qn(e)?(e.nodeName||"").toLowerCase():"#document"}function Xn(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Yn(e){var t;return null==(t=(qn(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function qn(e){return!!zn()&&(e instanceof Node||e instanceof Xn(e).Node)}function Zn(e){return!!zn()&&(e instanceof Element||e instanceof Xn(e).Element)}function Jn(e){return!!zn()&&(e instanceof HTMLElement||e instanceof Xn(e).HTMLElement)}function Qn(e){return!(!zn()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof Xn(e).ShadowRoot)}function er(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=ar(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function tr(e){return["table","td","th"].includes(Gn(e))}function nr(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(n){return!1}})}function rr(e){const t=or(),n=Zn(e)?ar(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function or(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function ir(e){return["html","body","#document"].includes(Gn(e))}function ar(e){return Xn(e).getComputedStyle(e)}function cr(e){return Zn(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function sr(e){if("html"===Gn(e))return e;const t=e.assignedSlot||e.parentNode||Qn(e)&&e.host||Yn(e);return Qn(t)?t.host:t}function ur(e){const t=sr(e);return ir(t)?e.ownerDocument?e.ownerDocument.body:e.body:Jn(t)&&er(t)?t:ur(t)}function lr(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=ur(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=Xn(o);if(i){const e=dr(a);return t.concat(a,a.visualViewport||[],er(o)?o:[],e&&n?lr(e):[])}return t.concat(o,lr(o,[],n))}function dr(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function fr(e){const t=ar(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Jn(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=Dn(n)!==i||Dn(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function pr(e){return Zn(e)?e:e.contextElement}function mr(e){const t=pr(e);if(!Jn(t))return Mn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=fr(t);let a=(i?Dn(n.width):n.width)/r,c=(i?Dn(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}const hr=Mn(0);function vr(e){const t=Xn(e);return or()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:hr}function gr(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=pr(e);let a=Mn(1);t&&(r?Zn(r)&&(a=mr(r)):a=mr(e));const c=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==Xn(e))&&t}(i,n,r)?vr(i):Mn(0);let s=(o.left+c.x)/a.x,u=(o.top+c.y)/a.y,l=o.width/a.x,d=o.height/a.y;if(i){const e=Xn(i),t=r&&Zn(r)?Xn(r):r;let n=e,o=dr(n);for(;o&&r&&t!==n;){const e=mr(o),t=o.getBoundingClientRect(),r=ar(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,u*=e.y,l*=e.x,d*=e.y,s+=i,u+=a,n=Xn(o),o=dr(n)}}return Kn({width:l,height:d,x:s,y:u})}function yr(e,t){const n=cr(e).scrollLeft;return t?t.left+n:gr(Yn(e)).left+n}function wr(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:yr(e,r)),y:r.top+t.scrollTop}}function br(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=Xn(e),r=Yn(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,c=0,s=0;if(o){i=o.width,a=o.height;const e=or();(!e||e&&"fixed"===t)&&(c=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:c,y:s}}(e,n);else if("document"===t)r=function(e){const t=Yn(e),n=cr(e),r=e.ownerDocument.body,o=Cn(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Cn(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+yr(e);const c=-n.scrollTop;return"rtl"===ar(r).direction&&(a+=Cn(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:c}}(Yn(e));else if(Zn(t))r=function(e,t){const n=gr(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Jn(e)?mr(e):Mn(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=vr(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Kn(r)}function xr(e,t){const n=sr(e);return!(n===t||!Zn(n)||ir(n))&&("fixed"===ar(n).position||xr(n,t))}function Er(e,t,n){const r=Jn(t),o=Yn(t),i="fixed"===n,a=gr(e,!0,i,t);let c={scrollLeft:0,scrollTop:0};const s=Mn(0);function u(){s.x=yr(o)}if(r||!r&&!i)if(("body"!==Gn(t)||er(o))&&(c=cr(t)),r){const e=gr(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&u();i&&!r&&o&&u();const l=!o||r||i?Mn(0):wr(o,c);return{x:a.left+c.scrollLeft-s.x-l.x,y:a.top+c.scrollTop-s.y-l.y,width:a.width,height:a.height}}function Rr(e){return"static"===ar(e).position}function Cr(e,t){if(!Jn(e)||"fixed"===ar(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Yn(e)===n&&(n=n.ownerDocument.body),n}function Dr(e,t){const n=Xn(e);if(nr(e))return n;if(!Jn(e)){let t=sr(e);for(;t&&!ir(t);){if(Zn(t)&&!Rr(t))return t;t=sr(t)}return n}let r=Cr(e,t);for(;r&&tr(r)&&Rr(r);)r=Cr(r,t);return r&&ir(r)&&Rr(r)&&!rr(r)?n:r||function(e){let t=sr(e);for(;Jn(t)&&!ir(t);){if(rr(t))return t;if(nr(t))return null;t=sr(t)}return null}(e)||n}const _r={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=Yn(r),c=!!t&&nr(t.floating);if(r===a||c&&i)return n;let s={scrollLeft:0,scrollTop:0},u=Mn(1);const l=Mn(0),d=Jn(r);if((d||!d&&!i)&&(("body"!==Gn(r)||er(a))&&(s=cr(r)),Jn(r))){const e=gr(r);u=mr(r),l.x=e.x+r.clientLeft,l.y=e.y+r.clientTop}const f=!a||d||i?Mn(0):wr(a,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+l.x+f.x,y:n.y*u.y-s.scrollTop*u.y+l.y+f.y}},getDocumentElement:Yn,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?nr(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=lr(e,[],!1).filter(e=>Zn(e)&&"body"!==Gn(e)),o=null;const i="fixed"===ar(e).position;let a=i?sr(e):e;for(;Zn(a)&&!ir(a);){const t=ar(a),n=rr(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||er(a)&&!n&&xr(e,a))?r=r.filter(e=>e!==a):o=t,a=sr(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],c=i.reduce((e,n)=>{const r=br(t,n,o);return e.top=Cn(r.top,e.top),e.right=Rn(r.right,e.right),e.bottom=Rn(r.bottom,e.bottom),e.left=Cn(r.left,e.left),e},br(t,a,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:Dr,getElementRects:async function(e){const t=this.getOffsetParent||Dr,n=this.getDimensions,r=await n(e.floating);return{reference:Er(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=fr(e);return{width:t,height:n}},getScale:mr,isElement:Zn,isRTL:function(e){return"rtl"===ar(e).direction}};function Mr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Sr(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,u=pr(e),l=o||i?[...u?lr(u):[],...lr(t)]:[];l.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});const d=u&&c?function(e,t){let n,r=null;const o=Yn(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),i();const u=e.getBoundingClientRect(),{left:l,top:d,width:f,height:p}=u;if(c||t(),!f||!p)return;const m={rootMargin:-_n(d)+"px "+-_n(o.clientWidth-(l+f))+"px "+-_n(o.clientHeight-(d+p))+"px "+-_n(l)+"px",threshold:Cn(0,Rn(1,s))||1};let h=!0;function v(t){const r=t[0].intersectionRatio;if(r!==s){if(!h)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||Mr(u,e.getBoundingClientRect())||a(),h=!1}try{r=new IntersectionObserver(v,{...m,root:o.ownerDocument})}catch(g){r=new IntersectionObserver(v,m)}r.observe(e)}(!0),i}(u,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[r]=e;r&&r.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),n()}),u&&!s&&m.observe(u),m.observe(t));let h=s?gr(e):null;return s&&function t(){const r=gr(e);h&&!Mr(h,r)&&n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;l.forEach(e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=m)||e.disconnect(),m=null,s&&cancelAnimationFrame(f)}}const Pr=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:c}=t,s=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=An(n),c=jn(n),s="y"===In(n),u=["left","top"].includes(a)?-1:1,l=i&&s?-1:1,d=Tn(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&"number"==typeof m&&(p="end"===c?-1*m:m),s?{x:p*l,y:f*u}:{x:f*u,y:p*l}}(t,e);return a===(null==(n=c.offset)?void 0:n.placement)&&null!=(r=c.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}},Or=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=Tn(e,t),u={x:n,y:r},l=await Hn(t,s),d=In(An(o)),f=Nn(d);let p=u[f],m=u[d];if(i){const e="y"===f?"bottom":"right";p=On(p+l["y"===f?"top":"left"],p,p-l[e])}if(a){const e="y"===d?"bottom":"right";m=On(m+l["y"===d?"top":"left"],m,m-l[e])}const h=c.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:a}}}}}},Tr=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:c,platform:s,elements:u}=t,{mainAxis:l=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=Tn(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=An(o),y=In(c),w=An(c)===c,b=await(null==s.isRTL?void 0:s.isRTL(u.floating)),x=f||(w||!h?[Wn(c)]:function(e){const t=Wn(e);return[Fn(e),t,Fn(t)]}(c)),E="none"!==m;!f&&E&&x.push(...function(e,t,n,r){const o=jn(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(An(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(Fn)))),i}(c,h,m,b));const R=[c,...x],C=await Hn(t,v),D=[];let _=(null==(r=i.flip)?void 0:r.overflows)||[];if(l&&D.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=jn(e),o=Ln(e),i=kn(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Wn(a)),[a,Wn(a)]}(o,a,b);D.push(C[e[0]],C[e[1]])}if(_=[..._,{placement:o,overflows:D}],!D.every(e=>e<=0)){var M,S;const e=((null==(M=i.flip)?void 0:M.index)||0)+1,t=R[e];if(t){if(!("alignment"===d&&y!==In(t))||_.every(e=>e.overflows[0]>0&&In(e.placement)===y))return{data:{index:e,overflows:_},reset:{placement:t}}}let n=null==(S=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:S.placement;if(!n)switch(p){case"bestFit":{var P;const e=null==(P=_.filter(e=>{if(E){const t=In(e.placement);return t===y||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:P[0];e&&(n=e);break}case"initialPlacement":n=c}if(o!==n)return{reset:{placement:n}}}return{}}}},Ar=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:c}=t,{apply:s=()=>{},...u}=Tn(e,t),l=await Hn(t,u),d=An(o),f=jn(o),p="y"===In(o),{width:m,height:h}=i.floating;let v,g;"top"===d||"bottom"===d?(v=d,g=f===(await(null==a.isRTL?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(g=d,v="end"===f?"top":"bottom");const y=h-l.top-l.bottom,w=m-l.left-l.right,b=Rn(h-l[v],y),x=Rn(m-l[g],w),E=!t.middlewareData.shift;let R=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!f){const e=Cn(l.left,0),t=Cn(l.right,0),n=Cn(l.top,0),r=Cn(l.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:Cn(l.left,l.right)):R=h-2*(0!==n||0!==r?n+r:Cn(l.top,l.bottom))}await s({...t,availableWidth:C,availableHeight:R});const D=await a.getDimensions(c.floating);return m!==D.width||h!==D.height?{reset:{rects:!0}}:{}}}},jr=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Tn(e,t);switch(r){case"referenceHidden":{const e=Vn(await Hn(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Un(e)}}}case"escaped":{const e=Vn(await Hn(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:Un(e)}}}default:return{}}}}},Nr=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:a,elements:c,middlewareData:s}=t,{element:u,padding:l=0}=Tn(e,t)||{};if(null==u)return{};const d=Bn(l),f={x:n,y:r},p=Ln(o),m=kn(p),h=await a.getDimensions(u),v="y"===p,g=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],x=f[p]-i.reference[p],E=await(null==a.getOffsetParent?void 0:a.getOffsetParent(u));let R=E?E[w]:0;R&&await(null==a.isElement?void 0:a.isElement(E))||(R=c.floating[w]||i.floating[m]);const C=b/2-x/2,D=R/2-h[m]/2-1,_=Rn(d[g],D),M=Rn(d[y],D),S=_,P=R-h[m]-M,O=R/2-h[m]/2+C,T=On(S,O,P),A=!s.arrow&&null!=jn(o)&&O!==T&&i.reference[m]/2-(O<S?_:M)-h[m]/2<0,j=A?O<S?O-S:O-P:0;return{[p]:f[p]+j,data:{[p]:T,centerOffset:O-T-j,...A&&{alignmentOffset:j}},reset:A}}}),kr=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:c=0,mainAxis:s=!0,crossAxis:u=!0}=Tn(e,t),l={x:n,y:r},d=In(o),f=Nn(d);let p=l[f],m=l[d];const h=Tn(c,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(An(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},Ir=(e,t,n)=>{const r=new Map,o={platform:_r,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,c=i.filter(Boolean),s=await(null==a.isRTL?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:l,y:d}=$n(u,r,s),f=r,p={},m=0;for(let h=0;h<c.length;h++){const{name:n,fn:i}=c[h],{x:v,y:g,data:y,reset:w}=await i({x:l,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});l=null!=v?v:l,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:l,y:d}=$n(u,f,s))),h=-1)}return{x:l,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var Lr="undefined"!=typeof document?e.useLayoutEffect:function(){};function Fr(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Fr(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Fr(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function Wr(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Br(e,t){const n=Wr(e);return Math.round(t*n)/n}function Kr(t){const n=e.useRef(t);return Lr(()=>{n.current=t}),n}const $r=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Nr({element:n.current,padding:r}).fn(t):{}:n?Nr({element:n,padding:r}).fn(t):{};var o}}),Hr=(e,t)=>({...Pr(e),options:[e,t]}),Vr=(e,t)=>({...Or(e),options:[e,t]}),Ur=(e,t)=>({...kr(e),options:[e,t]}),zr=(e,t)=>({...Tr(e),options:[e,t]}),Gr=(e,t)=>({...Ar(e),options:[e,t]}),Xr=(e,t)=>({...jr(e),options:[e,t]}),Yr=(e,t)=>({...$r(e),options:[e,t]});var qr=e.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return l.jsx(_.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});qr.displayName="Arrow";var Zr=qr,Jr="Popper",[Qr,eo]=b(Jr),[to,no]=Qr(Jr),ro=t=>{const{__scopePopper:n,children:r}=t,[o,i]=e.useState(null);return l.jsx(to,{scope:n,anchor:o,onAnchorChange:i,children:r})};ro.displayName=Jr;var oo="PopperAnchor",io=e.forwardRef((t,n)=>{const{__scopePopper:r,virtualRef:o,...i}=t,a=no(oo,r),c=e.useRef(null),s=p(n,c);return e.useEffect(()=>{a.onAnchorChange(o?.current||c.current)}),o?null:l.jsx(_.div,{...i,ref:s})});io.displayName=oo;var ao="PopperContent",[co,so]=Qr(ao),uo=e.forwardRef((t,r)=>{const{__scopePopper:o,side:i="bottom",sideOffset:a=0,align:c="center",alignOffset:s=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=t,b=no(ao,o),[x,R]=e.useState(null),C=p(r,e=>R(e)),[M,S]=e.useState(null),P=D(M),O=P?.width??0,T=P?.height??0,j=i+("center"!==c?"-"+c:""),N="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},k=Array.isArray(f)?f:[f],I=k.length>0,L={padding:N,boundary:k.filter(mo),altBoundary:I},{refs:F,floatingStyles:W,placement:B,isPositioned:K,middlewareData:$}=function(t){void 0===t&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:c,floating:s}={},transform:u=!0,whileElementsMounted:l,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);Fr(m,i)||h(i);const[v,g]=e.useState(null),[y,w]=e.useState(null),b=e.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=e.useCallback(e=>{e!==D.current&&(D.current=e,w(e))},[]),E=c||v,R=s||y,C=e.useRef(null),D=e.useRef(null),_=e.useRef(f),M=null!=l,S=Kr(l),P=Kr(a),O=Kr(d),T=e.useCallback(()=>{if(!C.current||!D.current)return;const e={placement:r,strategy:o,middleware:m};P.current&&(e.platform=P.current),Ir(C.current,D.current,e).then(e=>{const t={...e,isPositioned:!1!==O.current};A.current&&!Fr(_.current,t)&&(_.current=t,n.flushSync(()=>{p(t)}))})},[m,r,o,P,O]);Lr(()=>{!1===d&&_.current.isPositioned&&(_.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);const A=e.useRef(!1);Lr(()=>(A.current=!0,()=>{A.current=!1}),[]),Lr(()=>{if(E&&(C.current=E),R&&(D.current=R),E&&R){if(S.current)return S.current(E,R,T);T()}},[E,R,T,S,M]);const j=e.useMemo(()=>({reference:C,floating:D,setReference:b,setFloating:x}),[b,x]),N=e.useMemo(()=>({reference:E,floating:R}),[E,R]),k=e.useMemo(()=>{const e={position:o,left:0,top:0};if(!N.floating)return e;const t=Br(N.floating,f.x),n=Br(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...Wr(N.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}},[o,u,N.floating,f.x,f.y]);return e.useMemo(()=>({...f,update:T,refs:j,elements:N,floatingStyles:k}),[f,T,j,N,k])}({strategy:"fixed",placement:j,whileElementsMounted:(...e)=>Sr(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[Hr({mainAxis:a+T,alignmentAxis:s}),d&&Vr({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?Ur():void 0,...L}),d&&zr({...L}),Gr({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),M&&Yr({element:M,padding:u}),ho({arrowWidth:O,arrowHeight:T}),v&&Xr({strategy:"referenceHidden",...L})]}),[H,V]=vo(B),U=A(y);E(()=>{K&&U?.()},[K,U]);const z=$.arrow?.x,G=$.arrow?.y,X=0!==$.arrow?.centerOffset,[Y,q]=e.useState();return E(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),l.jsx("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:K?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[$.transformOrigin?.x,$.transformOrigin?.y].join(" "),...$.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:l.jsx(co,{scope:o,placedSide:H,onArrowChange:S,arrowX:z,arrowY:G,shouldHideArrow:X,children:l.jsx(_.div,{"data-side":H,"data-align":V,...w,ref:C,style:{...w.style,animation:K?void 0:"none"}})})})});uo.displayName=ao;var lo="PopperArrow",fo={top:"bottom",right:"left",bottom:"top",left:"right"},po=e.forwardRef(function(e,t){const{__scopePopper:n,...r}=e,o=so(lo,n),i=fo[o.placedSide];return l.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:l.jsx(Zr,{...r,ref:t,style:{...r.style,display:"block"}})})});function mo(e){return null!==e}po.displayName=lo;var ho=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=0!==o.arrow?.centerOffset,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[s,u]=vo(n),l={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+c/2;let p="",m="";return"bottom"===s?(p=i?l:`${d}px`,m=-c+"px"):"top"===s?(p=i?l:`${d}px`,m=`${r.floating.height+c}px`):"right"===s?(p=-c+"px",m=i?l:`${f}px`):"left"===s&&(p=`${r.floating.width+c}px`,m=i?l:`${f}px`),{data:{x:p,y:m}}}});function vo(e){const[t,n="center"]=e.split("-");return[t,n]}var go=ro,yo=io,wo=uo,bo=po,xo=["Enter"," "],Eo=["ArrowUp","PageDown","End"],Ro=["ArrowDown","PageUp","Home",...Eo],Co={ltr:[...xo,"ArrowRight"],rtl:[...xo,"ArrowLeft"]},Do={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_o="Menu",[Mo,So,Po]=S(_o),[Oo,To]=b(_o,[Po,eo,$]),Ao=eo(),jo=$(),[No,ko]=Oo(_o),[Io,Lo]=Oo(_o),Fo=t=>{const{__scopeMenu:n,open:r=!1,children:o,dir:i,onOpenChange:a,modal:c=!0}=t,s=Ao(n),[u,d]=e.useState(null),f=e.useRef(!1),p=A(a),m=N(i);return e.useEffect(()=>{const e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),l.jsx(go,{...s,children:l.jsx(No,{scope:n,open:r,onOpenChange:p,content:u,onContentChange:d,children:l.jsx(Io,{scope:n,onClose:e.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:c,children:o})})})};Fo.displayName=_o;var Wo=e.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Ao(n);return l.jsx(yo,{...o,...r,ref:t})});Wo.displayName="MenuAnchor";var Bo="MenuPortal",[Ko,$o]=Oo(Bo,{forceMount:void 0}),Ho=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=ko(Bo,t);return l.jsx(Ko,{scope:t,forceMount:n,children:l.jsx(Q,{present:n||i.open,children:l.jsx(Fe,{asChild:!0,container:o,children:r})})})};Ho.displayName=Bo;var Vo="MenuContent",[Uo,zo]=Oo(Vo),Go=e.forwardRef((e,t)=>{const n=$o(Vo,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=ko(Vo,e.__scopeMenu),a=Lo(Vo,e.__scopeMenu);return l.jsx(Mo.Provider,{scope:e.__scopeMenu,children:l.jsx(Q,{present:r||i.open,children:l.jsx(Mo.Slot,{scope:e.__scopeMenu,children:a.modal?l.jsx(Xo,{...o,ref:t}):l.jsx(Yo,{...o,ref:t})})})})}),Xo=e.forwardRef((t,n)=>{const r=ko(Vo,t.__scopeMenu),o=e.useRef(null),i=p(n,o);return e.useEffect(()=>{const e=o.current;if(e)return kt(e)},[]),l.jsx(Zo,{...t,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:w(t.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Yo=e.forwardRef((e,t)=>{const n=ko(Vo,e.__scopeMenu);return l.jsx(Zo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),qo=m("MenuContent.ScrollLock"),Zo=e.forwardRef((t,n)=>{const{__scopeMenu:r,loop:o=!1,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:u,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:h,onDismiss:v,disableOutsideScroll:g,...y}=t,b=ko(Vo,r),x=Lo(Vo,r),E=Ao(r),R=jo(r),C=So(r),[D,_]=e.useState(null),M=e.useRef(null),S=p(n,M,b.onContentChange),P=e.useRef(0),O=e.useRef(""),T=e.useRef(0),A=e.useRef(null),j=e.useRef("right"),N=e.useRef(0),k=g?St:e.Fragment,I=g?{as:qo,allowPinchZoom:!0}:void 0,L=e=>{const t=O.current+e,n=C().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){const r=t.length>1&&Array.from(t).every(e=>e===t[0]),o=r?t[0]:t,i=n?e.indexOf(n):-1;let a=(c=e,s=Math.max(i,0),c.map((e,t)=>c[(s+t)%c.length]));var c,s;1===o.length&&(a=a.filter(e=>e!==n));const u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){O.current=t,window.clearTimeout(P.current),""!==t&&(P.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};e.useEffect(()=>()=>window.clearTimeout(P.current),[]),Be();const F=e.useCallback(e=>j.current===A.current?.side&&function(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return function(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,a=t.length-1;i<t.length;a=i++){const e=t[i],c=t[a],s=e.x,u=e.y,l=c.x,d=c.y;u>r!=d>r&&n<(l-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,t)}(e,A.current?.area),[]);return l.jsx(Uo,{scope:r,searchRef:O,onItemEnter:e.useCallback(e=>{F(e)&&e.preventDefault()},[F]),onItemLeave:e.useCallback(e=>{F(e)||(M.current?.focus(),_(null))},[F]),onTriggerLeave:e.useCallback(e=>{F(e)&&e.preventDefault()},[F]),pointerGraceTimerRef:T,onPointerGraceIntentChange:e.useCallback(e=>{A.current=e},[]),children:l.jsx(k,{...I,children:l.jsx(Te,{asChild:!0,trapped:i,onMountAutoFocus:w(a,e=>{e.preventDefault(),M.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:l.jsx(De,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:h,onDismiss:v,children:l.jsx(Z,{asChild:!0,...R,dir:x.dir,orientation:"vertical",loop:o,currentTabStopId:D,onCurrentTabStopIdChange:_,onEntryFocus:w(u,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:l.jsx(wo,{role:"menu","aria-orientation":"vertical","data-state":Ri(b.open),"data-radix-menu-content":"",dir:x.dir,...E,...y,ref:S,style:{outline:"none",...y.style},onKeyDown:w(y.onKeyDown,e=>{const t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&L(e.key));const o=M.current;if(e.target!==o)return;if(!Ro.includes(e.key))return;e.preventDefault();const i=C().filter(e=>!e.disabled).map(e=>e.ref.current);Eo.includes(e.key)&&i.reverse(),function(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}(i)}),onBlur:w(t.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(P.current),O.current="")}),onPointerMove:w(t.onPointerMove,_i(e=>{const t=e.target,n=N.current!==e.clientX;if(e.currentTarget.contains(t)&&n){const t=e.clientX>N.current?"right":"left";j.current=t,N.current=e.clientX}}))})})})})})})});Go.displayName=Vo;var Jo=e.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(_.div,{role:"group",...r,ref:t})});Jo.displayName="MenuGroup";var Qo=e.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(_.div,{...r,ref:t})});Qo.displayName="MenuLabel";var ei="MenuItem",ti="menu.itemSelect",ni=e.forwardRef((t,n)=>{const{disabled:r=!1,onSelect:o,...i}=t,a=e.useRef(null),c=Lo(ei,t.__scopeMenu),s=zo(ei,t.__scopeMenu),u=p(n,a),d=e.useRef(!1);return l.jsx(ri,{...i,ref:u,disabled:r,onClick:w(t.onClick,()=>{const e=a.current;if(!r&&e){const t=new CustomEvent(ti,{bubbles:!0,cancelable:!0});e.addEventListener(ti,e=>o?.(e),{once:!0}),M(e,t),t.defaultPrevented?d.current=!1:c.onClose()}}),onPointerDown:e=>{t.onPointerDown?.(e),d.current=!0},onPointerUp:w(t.onPointerUp,e=>{d.current||e.currentTarget?.click()}),onKeyDown:w(t.onKeyDown,e=>{const t=""!==s.searchRef.current;r||t&&" "===e.key||xo.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ni.displayName=ei;var ri=e.forwardRef((t,n)=>{const{__scopeMenu:r,disabled:o=!1,textValue:i,...a}=t,c=zo(ei,r),s=jo(r),u=e.useRef(null),d=p(n,u),[f,m]=e.useState(!1),[h,v]=e.useState("");return e.useEffect(()=>{const e=u.current;e&&v((e.textContent??"").trim())},[a.children]),l.jsx(Mo.ItemSlot,{scope:r,disabled:o,textValue:i??h,children:l.jsx(J,{asChild:!0,...s,focusable:!o,children:l.jsx(_.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...a,ref:d,onPointerMove:w(t.onPointerMove,_i(e=>{if(o)c.onItemLeave(e);else if(c.onItemEnter(e),!e.defaultPrevented){e.currentTarget.focus({preventScroll:!0})}})),onPointerLeave:w(t.onPointerLeave,_i(e=>c.onItemLeave(e))),onFocus:w(t.onFocus,()=>m(!0)),onBlur:w(t.onBlur,()=>m(!1))})})})}),oi=e.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return l.jsx(fi,{scope:e.__scopeMenu,checked:n,children:l.jsx(ni,{role:"menuitemcheckbox","aria-checked":Ci(n)?"mixed":n,...o,ref:t,"data-state":Di(n),onSelect:w(o.onSelect,()=>r?.(!!Ci(n)||!n),{checkForDefaultPrevented:!1})})})});oi.displayName="MenuCheckboxItem";var ii="MenuRadioGroup",[ai,ci]=Oo(ii,{value:void 0,onValueChange:()=>{}}),si=e.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,i=A(r);return l.jsx(ai,{scope:e.__scopeMenu,value:n,onValueChange:i,children:l.jsx(Jo,{...o,ref:t})})});si.displayName=ii;var ui="MenuRadioItem",li=e.forwardRef((e,t)=>{const{value:n,...r}=e,o=ci(ui,e.__scopeMenu),i=n===o.value;return l.jsx(fi,{scope:e.__scopeMenu,checked:i,children:l.jsx(ni,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":Di(i),onSelect:w(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});li.displayName=ui;var di="MenuItemIndicator",[fi,pi]=Oo(di,{checked:!1}),mi=e.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,i=pi(di,n);return l.jsx(Q,{present:r||Ci(i.checked)||!0===i.checked,children:l.jsx(_.span,{...o,ref:t,"data-state":Di(i.checked)})})});mi.displayName=di;var hi=e.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(_.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});hi.displayName="MenuSeparator";var vi=e.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Ao(n);return l.jsx(bo,{...o,...r,ref:t})});vi.displayName="MenuArrow";var[gi,yi]=Oo("MenuSub"),wi="MenuSubTrigger",bi=e.forwardRef((t,n)=>{const r=ko(wi,t.__scopeMenu),o=Lo(wi,t.__scopeMenu),i=yi(wi,t.__scopeMenu),a=zo(wi,t.__scopeMenu),c=e.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:u}=a,d={__scopeMenu:t.__scopeMenu},p=e.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return e.useEffect(()=>p,[p]),e.useEffect(()=>{const e=s.current;return()=>{window.clearTimeout(e),u(null)}},[s,u]),l.jsx(Wo,{asChild:!0,...d,children:l.jsx(ri,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":Ri(r.open),...t,ref:f(n,i.onTriggerChange),onClick:e=>{t.onClick?.(e),t.disabled||e.defaultPrevented||(e.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:w(t.onPointerMove,_i(e=>{a.onItemEnter(e),e.defaultPrevented||t.disabled||r.open||c.current||(a.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:w(t.onPointerLeave,_i(e=>{p();const t=r.content?.getBoundingClientRect();if(t){const n=r.content?.dataset.side,o="right"===n,i=o?-5:5,c=t[o?"left":"right"],u=t[o?"right":"left"];a.onPointerGraceIntentChange({area:[{x:e.clientX+i,y:e.clientY},{x:c,y:t.top},{x:u,y:t.top},{x:u,y:t.bottom},{x:c,y:t.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(e),e.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:w(t.onKeyDown,e=>{const n=""!==a.searchRef.current;t.disabled||n&&" "===e.key||Co[o.dir].includes(e.key)&&(r.onOpenChange(!0),r.content?.focus(),e.preventDefault())})})})});bi.displayName=wi;var xi="MenuSubContent",Ei=e.forwardRef((t,n)=>{const r=$o(Vo,t.__scopeMenu),{forceMount:o=r.forceMount,...i}=t,a=ko(Vo,t.__scopeMenu),c=Lo(Vo,t.__scopeMenu),s=yi(xi,t.__scopeMenu),u=e.useRef(null),d=p(n,u);return l.jsx(Mo.Provider,{scope:t.__scopeMenu,children:l.jsx(Q,{present:o||a.open,children:l.jsx(Mo.Slot,{scope:t.__scopeMenu,children:l.jsx(Zo,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&u.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:w(t.onFocusOutside,e=>{e.target!==s.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:w(t.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:w(t.onKeyDown,e=>{const t=e.currentTarget.contains(e.target),n=Do[c.dir].includes(e.key);t&&n&&(a.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function Ri(e){return e?"open":"closed"}function Ci(e){return"indeterminate"===e}function Di(e){return Ci(e)?"indeterminate":e?"checked":"unchecked"}function _i(e){return t=>"mouse"===t.pointerType?e(t):void 0}Ei.displayName=xi;var Mi=Fo,Si=Wo,Pi=Ho,Oi=Go,Ti=Jo,Ai=Qo,ji=ni,Ni=oi,ki=si,Ii=li,Li=mi,Fi=hi,Wi=vi,Bi=bi,Ki=Ei,$i="DropdownMenu",[Hi,Vi]=b($i,[To]),Ui=To(),[zi,Gi]=Hi($i),Xi=t=>{const{__scopeDropdownMenu:n,children:r,dir:o,open:i,defaultOpen:a,onOpenChange:c,modal:s=!0}=t,u=Ui(n),d=e.useRef(null),[f,p]=C({prop:i,defaultProp:a??!1,onChange:c,caller:$i});return l.jsx(zi,{scope:n,triggerId:T(),triggerRef:d,contentId:T(),open:f,onOpenChange:p,onOpenToggle:e.useCallback(()=>p(e=>!e),[p]),modal:s,children:l.jsx(Mi,{...u,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};Xi.displayName=$i;var Yi="DropdownMenuTrigger",qi=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=Gi(Yi,n),a=Ui(n);return l.jsx(Si,{asChild:!0,...a,children:l.jsx(_.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:f(t,i.triggerRef),onPointerDown:w(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:w(e.onKeyDown,e=>{r||(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});qi.displayName=Yi;var Zi=e=>{const{__scopeDropdownMenu:t,...n}=e,r=Ui(t);return l.jsx(Pi,{...r,...n})};Zi.displayName="DropdownMenuPortal";var Ji="DropdownMenuContent",Qi=e.forwardRef((t,n)=>{const{__scopeDropdownMenu:r,...o}=t,i=Gi(Ji,r),a=Ui(r),c=e.useRef(!1);return l.jsx(Oi,{id:i.contentId,"aria-labelledby":i.triggerId,...a,...o,ref:n,onCloseAutoFocus:w(t.onCloseAutoFocus,e=>{c.current||i.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:w(t.onInteractOutside,e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;i.modal&&!r||(c.current=!0)}),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Qi.displayName=Ji;e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Ti,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var ea=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Ai,{...o,...r,ref:t})});ea.displayName="DropdownMenuLabel";var ta=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(ji,{...o,...r,ref:t})});ta.displayName="DropdownMenuItem";var na=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Ni,{...o,...r,ref:t})});na.displayName="DropdownMenuCheckboxItem";e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(ki,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup";var ra=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Ii,{...o,...r,ref:t})});ra.displayName="DropdownMenuRadioItem";var oa=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Li,{...o,...r,ref:t})});oa.displayName="DropdownMenuItemIndicator";var ia=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Fi,{...o,...r,ref:t})});ia.displayName="DropdownMenuSeparator";e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Wi,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var aa=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Bi,{...o,...r,ref:t})});aa.displayName="DropdownMenuSubTrigger";var ca=e.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ui(n);return l.jsx(Ki,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ca.displayName="DropdownMenuSubContent";var sa=Xi,ua=qi,la=Zi,da=Qi,fa=ea,pa=ta,ma=na,ha=ra,va=oa,ga=ia,ya=aa,wa=ca;export{da as C,bn as D,pa as I,fa as L,gn as O,la as P,sa as R,h as S,ua as T,ya as a,wa as b,ma as c,va as d,ha as e,ga as f,C as g,b as h,_ as i,l as j,w as k,D as l,hn as m,vn as n,yn as o,xn as p,wn as q,ve as r,ge as s,ye as t,p as u,we as v};
