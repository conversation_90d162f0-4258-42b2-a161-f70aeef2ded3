import{r as e,g as t,a as n}from"./vendor-DEQ385Nk.js";function r(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var a=e();const o=t(a),i=r({__proto__:null,default:o},[a]);var l=n();const s=t(l);
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}var c,h;(h=c||(c={})).Pop="POP",h.Push="PUSH",h.Replace="REPLACE";const p="popstate";function d(e){return void 0===e&&(e={}),function(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,l=c.Pop,s=null,h=d();null==h&&(h=0,i.replaceState(u({},i.state,{idx:h}),""));function d(){return(i.state||{idx:null}).idx}function m(){l=c.Pop;let e=d(),t=null==e?null:e-h;h=e,s&&s({action:l,location:C.location,delta:t})}function b(e,t){l=c.Push;let n=g(C.location,e,t);h=d()+1;let r=v(n,h),u=C.createHref(n);try{i.pushState(r,"",u)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;a.location.assign(u)}o&&s&&s({action:l,location:C.location,delta:1})}function w(e,t){l=c.Replace;let n=g(C.location,e,t);h=d();let r=v(n,h),a=C.createHref(n);i.replaceState(r,"",a),o&&s&&s({action:l,location:C.location,delta:0})}function x(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:y(e);return n=n.replace(/ $/,"%20"),f(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let C={get action(){return l},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(p,m),s=e,()=>{a.removeEventListener(p,m),s=null}},createHref:e=>t(a,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:b,replace:w,go:e=>i.go(e)};return C}(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return g("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:y(t)},0,e)}function f(e,t){if(!1===e||null==e)throw new Error(t)}function m(e,t){if(!e)try{throw new Error(t)}catch(n){}}function v(e,t){return{usr:e.state,key:e.key,idx:t}}function g(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?b(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function y(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function b(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var w,x;function C(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let r="string"==typeof t?b(t):t,a=D(r.pathname||"/",n);if(null==a)return null;let o=E(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(o);let i=null;for(let l=0;null==i&&l<o.length;++l){let e=W(a);i=B(o[l],e)}return i}(e,t,n)}function E(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(f(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let l=A([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(f(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),E(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:T(l,e.index),routesMeta:s})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of S(e.path))a(e,t,r);else a(e,t)}),t}function S(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=S(r.join("/")),l=[];return l.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}(x=w||(w={})).data="data",x.deferred="deferred",x.redirect="redirect",x.error="error";const R=/^:[\w-]+$/,P=3,O=2,L=1,U=10,k=-2,j=e=>"*"===e;function T(e,t){let n=e.split("/"),r=n.length;return n.some(j)&&(r+=k),t&&(r+=O),n.filter(e=>!j(e)).reduce((e,t)=>e+(R.test(t)?P:""===t?L:U),r)}function B(e,t,n){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],n=l===r.length-1,s="/"===o?t:t.slice(o.length)||"/",u=N({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},s),c=e.route;if(!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:A([o,u.pathname]),pathnameBase:M(A([o,u.pathnameBase])),route:c}),"/"!==u.pathnameBase&&(o=A([o,u.pathnameBase]))}return i}function N(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);m("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=l[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function W(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return m(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function D(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function $(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function F(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function _(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=b(e):(a=u({},e),f(!a.pathname||!a.pathname.includes("?"),$("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),$("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),$("#","search","hash",a)));let o,i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?b(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:I(r),hash:H(a)}}(a,o),c=l&&"/"!==l&&l.endsWith("/"),h=(i||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!h||(s.pathname+="/"),s}const A=e=>e.join("/").replace(/\/\/+/g,"/"),M=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",H=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const J=["post","put","patch","delete"];new Set(J);const z=["get",...J];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(this,arguments)}new Set(z);const K=a.createContext(null),q=a.createContext(null),G=a.createContext(null),Q=a.createContext(null),X=a.createContext({outlet:null,matches:[],isDataRoute:!1}),Y=a.createContext(null);function Z(){return null!=a.useContext(Q)}function ee(){return Z()||f(!1),a.useContext(Q).location}function te(e){a.useContext(G).static||a.useLayoutEffect(e)}function ne(){let{isDataRoute:e}=a.useContext(X);return e?function(){let{router:e}=function(){let e=a.useContext(K);return e||f(!1),e}(ue.UseNavigateStable),t=he(ce.UseNavigateStable),n=a.useRef(!1);return te(()=>{n.current=!0}),a.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,V({fromRouteId:t},a)))},[e,t])}():function(){Z()||f(!1);let e=a.useContext(K),{basename:t,future:n,navigator:r}=a.useContext(G),{matches:o}=a.useContext(X),{pathname:i}=ee(),l=JSON.stringify(F(o,n.v7_relativeSplatPath)),s=a.useRef(!1);return te(()=>{s.current=!0}),a.useCallback(function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"==typeof n)return void r.go(n);let o=_(n,JSON.parse(l),i,"path"===a.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:A([t,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)},[t,r,l,i,e])}()}function re(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(G),{matches:o}=a.useContext(X),{pathname:i}=ee(),l=JSON.stringify(F(o,r.v7_relativeSplatPath));return a.useMemo(()=>_(e,JSON.parse(l),i,"path"===n),[e,l,i,n])}function ae(e,t){return function(e,t,n,r){Z()||f(!1);let{navigator:o}=a.useContext(G),{matches:i}=a.useContext(X),l=i[i.length-1],s=l?l.params:{};!l||l.pathname;let u=l?l.pathnameBase:"/";l&&l.route;let h,p=ee();if(t){var d;let e="string"==typeof t?b(t):t;"/"===u||(null==(d=e.pathname)?void 0:d.startsWith(u))||f(!1),h=e}else h=p;let m=h.pathname||"/",v=m;if("/"!==u){let e=u.replace(/^\//,"").split("/");v="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=C(e,{pathname:v}),y=function(e,t,n,r){var o;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===r&&(r=null);if(null==e){var i;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(i=r)&&i.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let l=e,s=null==(o=n)?void 0:o.errors;if(null!=s){let e=l.findIndex(e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id]));e>=0||f(!1),l=l.slice(0,Math.min(l.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<l.length;a++){let e=l[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,l=c>=0?l.slice(0,c+1):[l[0]];break}}}return l.reduceRight((e,r,o)=>{let i,h=!1,p=null,d=null;var f;n&&(i=s&&r.route.id?s[r.route.id]:void 0,p=r.route.errorElement||ie,u&&(c<0&&0===o?(pe[f="route-fallback"]||(pe[f]=!0),h=!0,d=null):c===o&&(h=!0,d=r.route.hydrateFallbackElement||null)));let m=t.concat(l.slice(0,o+1)),v=()=>{let t;return t=i?p:h?d:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(se,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?a.createElement(le,{location:n.location,revalidation:n.revalidation,component:p,error:i,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()},null)}(g&&g.map(e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:A([u,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:A([u,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);if(t&&y)return a.createElement(Q.Provider,{value:{location:V({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:c.Pop}},y);return y}(e,t)}function oe(){let e=function(){var e;let t=a.useContext(Y),n=function(){let e=a.useContext(q);return e||f(!1),e}(),r=he();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:r},n):null,null)}const ie=a.createElement(oe,null);class le extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?a.createElement(X.Provider,{value:this.props.routeContext},a.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function se(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(K);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(X.Provider,{value:t},r)}var ue=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ue||{}),ce=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ce||{});function he(e){let t=function(){let e=a.useContext(X);return e||f(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||f(!1),n.route.id}const pe={};function de(e){let{to:t,replace:n,state:r,relative:o}=e;Z()||f(!1);let{future:i,static:l}=a.useContext(G),{matches:s}=a.useContext(X),{pathname:u}=ee(),c=ne(),h=_(t,F(s,i.v7_relativeSplatPath),u,"path"===o),p=JSON.stringify(h);return a.useEffect(()=>c(JSON.parse(p),{replace:n,state:r,relative:o}),[c,p,o,n,r]),null}function fe(e){f(!1)}function me(e){let{basename:t="/",children:n=null,location:r,navigationType:o=c.Pop,navigator:i,static:l=!1,future:s}=e;Z()&&f(!1);let u=t.replace(/^\/*/,"/"),h=a.useMemo(()=>({basename:u,navigator:i,static:l,future:V({v7_relativeSplatPath:!1},s)}),[u,s,i,l]);"string"==typeof r&&(r=b(r));let{pathname:p="/",search:d="",hash:m="",state:v=null,key:g="default"}=r,y=a.useMemo(()=>{let e=D(p,u);return null==e?null:{location:{pathname:e,search:d,hash:m,state:v,key:g},navigationType:o}},[u,p,d,m,v,g,o]);return null==y?null:a.createElement(G.Provider,{value:h},a.createElement(Q.Provider,{children:n,value:y}))}function ve(e){let{children:t,location:n}=e;return ae(ge(t),n)}function ge(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,(e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,ge(e.props.children,o));e.type!==fe&&f(!1),e.props.index&&e.props.children&&f(!1);let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=ge(e.props.children,o)),n.push(i)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ye(){return ye=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ye.apply(this,arguments)}function be(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Promise(()=>{});const we=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],xe=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"];try{window.__reactRouterVersion="6"}catch(Be){}const Ce=a.createContext({isTransitioning:!1}),Ee=i.startTransition;function Se(e){let{basename:t,children:n,future:r,window:o}=e,i=a.useRef();null==i.current&&(i.current=d({window:o,v5Compat:!0}));let l=i.current,[s,u]=a.useState({action:l.action,location:l.location}),{v7_startTransition:c}=r||{},h=a.useCallback(e=>{c&&Ee?Ee(()=>u(e)):u(e)},[u,c]);return a.useLayoutEffect(()=>l.listen(h),[l,h]),a.useEffect(()=>{return null==(e=r)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[r]),a.createElement(me,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:l,future:r})}const Re="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Pe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Oe=a.forwardRef(function(e,t){let n,{onClick:r,relative:o,reloadDocument:i,replace:l,state:s,target:u,to:c,preventScrollReset:h,viewTransition:p}=e,d=be(e,we),{basename:m}=a.useContext(G),v=!1;if("string"==typeof c&&Pe.test(c)&&(n=c,Re))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=D(t.pathname,m);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:v=!0}catch(Be){}let g=function(e,t){let{relative:n}=void 0===t?{}:t;Z()||f(!1);let{basename:r,navigator:o}=a.useContext(G),{hash:i,pathname:l,search:s}=re(e,{relative:n}),u=l;return"/"!==r&&(u="/"===l?r:A([r,l])),o.createHref({pathname:u,search:s,hash:i})}(c,{relative:o}),b=function(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:l,viewTransition:s}=void 0===t?{}:t,u=ne(),c=ee(),h=re(e,{relative:l});return a.useCallback(t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==r?r:y(c)===y(h);u(e,{replace:n,state:o,preventScrollReset:i,relative:l,viewTransition:s})}},[c,u,h,r,o,n,e,i,l,s])}(c,{replace:l,state:s,target:u,preventScrollReset:h,relative:o,viewTransition:p});return a.createElement("a",ye({},d,{href:n||g,onClick:v||i?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))}),Le=a.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:o="",end:i=!1,style:l,to:s,viewTransition:u,children:c}=e,h=be(e,xe),p=re(s,{relative:h.relative}),d=ee(),m=a.useContext(q),{navigator:v,basename:g}=a.useContext(G),y=null!=m&&function(e,t){void 0===t&&(t={});let n=a.useContext(Ce);null==n&&f(!1);let{basename:r}=function(){let e=a.useContext(K);return e||f(!1),e}(Ue.useViewTransitionState),o=re(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=D(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=D(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=N(o.pathname,l)||null!=N(o.pathname,i)}(p)&&!0===u,b=v.encodeLocation?v.encodeLocation(p).pathname:p.pathname,w=d.pathname,x=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(w=w.toLowerCase(),x=x?x.toLowerCase():null,b=b.toLowerCase()),x&&g&&(x=D(x,g)||x);const C="/"!==b&&b.endsWith("/")?b.length-1:b.length;let E,S=w===b||!i&&w.startsWith(b)&&"/"===w.charAt(C),R=null!=x&&(x===b||!i&&x.startsWith(b)&&"/"===x.charAt(b.length)),P={isActive:S,isPending:R,isTransitioning:y},O=S?n:void 0;E="function"==typeof o?o(P):[o,S?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let L="function"==typeof l?l(P):l;return a.createElement(Oe,ye({},h,{"aria-current":O,className:E,ref:t,style:L,to:s,viewTransition:u}),"function"==typeof c?c(P):c)});var Ue,ke,je,Te;(ke=Ue||(Ue={})).UseScrollRestoration="useScrollRestoration",ke.UseSubmit="useSubmit",ke.UseSubmitFetcher="useSubmitFetcher",ke.UseFetcher="useFetcher",ke.useViewTransitionState="useViewTransitionState",(Te=je||(je={})).UseFetcher="useFetcher",Te.UseFetchers="useFetchers",Te.UseScrollRestoration="useScrollRestoration";export{Se as B,Oe as L,Le as N,i as R,l as a,o as b,s as c,ve as d,fe as e,de as f,a as r};
