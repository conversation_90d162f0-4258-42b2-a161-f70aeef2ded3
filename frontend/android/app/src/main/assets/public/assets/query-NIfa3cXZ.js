import{r as t}from"./router-B-XVe37S.js";import{j as e}from"./ui-B-v3Zwus.js";var s=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},i="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(t){return"number"==typeof t&&t>=0&&t!==1/0}function a(t,e){return Math.max(t+(e||0)-Date.now(),0)}function o(t,e){return"function"==typeof t?t(e):t}function u(t,e){return"function"==typeof t?t(e):t}function c(t,e){const{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==l(a,e.options))return!1}else if(!f(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!r||r===e.state.fetchStatus)&&!(n&&!n(e)))}function h(t,e){const{exact:s,status:i,predicate:r,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(d(e.options.mutationKey)!==d(n))return!1}else if(!f(e.options.mutationKey,n))return!1}return(!i||e.state.status===i)&&!(r&&!r(e))}function l(t,e){return(e?.queryKeyHashFn||d)(t)}function d(t){return JSON.stringify(t,(t,e)=>v(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function f(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every(s=>f(t[s],e[s])))}function p(t,e){if(t===e)return t;const s=m(t)&&m(e);if(s||v(t)&&v(e)){const i=s?t:Object.keys(t),r=i.length,n=s?e:Object.keys(e),a=n.length,o=s?[]:{},u=new Set(i);let c=0;for(let h=0;h<a;h++){const i=s?h:n[h];(!s&&u.has(i)||s)&&void 0===t[i]&&void 0===e[i]?(o[i]=void 0,c++):(o[i]=p(t[i],e[i]),o[i]===t[i]&&void 0!==t[i]&&c++)}return r===a&&c===r?t:o}return e}function y(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function m(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function v(t){if(!b(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!b(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?p(t,e):e}function O(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function C(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var R=Symbol();function w(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==R?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var S=new class extends s{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:"hidden"!==globalThis.document?.visibilityState}},Q=new class extends s{#i=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#i!==t&&(this.#i=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#i}};function q(){let t,e;const s=new Promise((s,i)=>{t=s,e=i});function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}function P(t){return Math.min(1e3*2**t,3e4)}function F(t){return"online"!==(t??"online")||Q.isOnline()}var E=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function D(t){return t instanceof E}function T(t){let e,s=!1,r=0,n=!1;const a=q(),o=()=>S.isFocused()&&("always"===t.networkMode||Q.isOnline())&&t.canRun(),u=()=>F(t.networkMode)&&t.canRun(),c=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),a.resolve(s))},h=s=>{n||(n=!0,t.onError?.(s),e?.(),a.reject(s))},l=()=>new Promise(s=>{e=t=>{(n||o())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,n||t.onContinue?.()}),d=()=>{if(n)return;let e;const a=0===r?t.initialPromise:void 0;try{e=a??t.fn()}catch(u){e=Promise.reject(u)}Promise.resolve(e).then(c).catch(e=>{if(n)return;const a=t.retry??(i?0:3),u=t.retryDelay??P,c="function"==typeof u?u(r,e):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,e);var p;!s&&f?(r++,t.onFail?.(r,e),(p=c,new Promise(t=>{setTimeout(t,p)})).then(()=>o()?void 0:l()).then(()=>{s?h(e):d()})):h(e)})};return{promise:a,cancel:e=>{n||(h(new E(e)),t.abort?.())},continue:()=>(e?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():l().then(d),a)}}var I=t=>setTimeout(t,0);var x=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},r=I;const n=i=>{e?t.push(i):r(()=>{s(i)})};return{batch:n=>{let a;e++;try{a=n()}finally{e--,e||(()=>{const e=t;t=[],e.length&&r(()=>{i(()=>{e.forEach(t=>{s(t)})})})})()}return a},batchCalls:t=>(...e)=>{n(()=>{t(...e)})},schedule:n,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}(),A=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),n(this.gcTime)&&(this.#r=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},M=class extends A{#n;#a;#o;#u;#c;#h;#l;constructor(t){super(),this.#l=!1,this.#h=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#u=t.client,this.#o=this.#u.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#n=function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(t){this.options={...this.#h,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){const s=g(this.state.data,t,this.options);return this.#d({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#d({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#c?.promise;return this.#c?.cancel(t),e?e.then(r).catch(r):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(t=>!1!==u(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===R||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===o(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!a(this.state.dataUpdatedAt,t))}onFocus(){const t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#c&&(this.#l?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}const s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#l=!0,s.signal)})},r=()=>{const t=w(this.options,e),s=(()=>{const t={client:this.#u,queryKey:this.queryKey,meta:this.meta};return i(t),t})();return this.#l=!1,this.options.persister?this.options.persister(t,s,this):t(s)},n=(()=>{const t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:r};return i(t),t})();this.options.behavior?.onFetch(n,this),this.#a=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#d({type:"fetch",meta:n.fetchOptions?.meta});const a=t=>{D(t)&&t.silent||this.#d({type:"error",error:t}),D(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#c=T({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(e){return void a(e)}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else a(new Error(`${this.queryHash} data is undefined`))},onError:a,onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...U(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#a=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return D(s)&&s.revert&&this.#a?{...this.#a,fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),x.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:t})})}};function U(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:F(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var j=class extends s{constructor(t={}){super(),this.config=t,this.#f=new Map}#f;build(t,e,s){const i=e.queryKey,r=e.queryHash??l(i,e);let n=this.get(r);return n||(n=new M({client:t,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(n)),n}add(t){this.#f.has(t.queryHash)||(this.#f.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#f.get(t.queryHash);e&&(t.destroy(),e===t&&this.#f.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){x.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#f.get(t)}getAll(){return[...this.#f.values()]}find(t){const e={exact:!0,...t};return this.getAll().find(t=>c(e,t))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter(e=>c(t,e)):e}notify(t){x.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){x.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){x.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},k=class extends A{#p;#y;#c;constructor(t){super(),this.mutationId=t.mutationId,this.#y=t.mutationCache,this.#p=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#p.includes(t)||(this.#p.push(t),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#p=this.#p.filter(e=>e!==t),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>{this.#d({type:"continue"})};this.#c=T({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});const s="pending"===this.state.status,i=!this.#c.canStart();try{if(s)e();else{this.#d({type:"pending",variables:t,isPaused:i}),await(this.#y.config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&this.#d({type:"pending",context:e,variables:t,isPaused:i})}const r=await this.#c.start();return await(this.#y.config.onSuccess?.(r,t,this.state.context,this)),await(this.options.onSuccess?.(r,t,this.state.context)),await(this.#y.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,t,this.state.context)),this.#d({type:"success",data:r}),r}catch(r){try{throw await(this.#y.config.onError?.(r,t,this.state.context,this)),await(this.options.onError?.(r,t,this.state.context)),await(this.#y.config.onSettled?.(void 0,r,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,r,t,this.state.context)),r}finally{this.#d({type:"error",error:r})}}finally{this.#y.runNext(this)}}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),x.batch(()=>{this.#p.forEach(e=>{e.onMutationUpdate(t)}),this.#y.notify({mutation:this,type:"updated",action:t})})}};var K=class extends s{constructor(t={}){super(),this.config=t,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(t,e,s){const i=new k({mutationCache:this,mutationId:++this.#b,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#m.add(t);const e=L(t);if("string"==typeof e){const s=this.#v.get(e);s?s.push(t):this.#v.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#m.delete(t)){const e=L(t);if("string"==typeof e){const s=this.#v.get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#v.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=L(t);if("string"==typeof e){const s=this.#v.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}return!0}runNext(t){const e=L(t);if("string"==typeof e){const s=this.#v.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}return Promise.resolve()}clear(){x.batch(()=>{this.#m.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(t){const e={exact:!0,...t};return this.getAll().find(t=>h(e,t))}findAll(t={}){return this.getAll().filter(e=>h(t,e))}notify(t){x.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){const t=this.getAll().filter(t=>t.state.isPaused);return x.batch(()=>Promise.all(t.map(t=>t.continue().catch(r))))}};function L(t){return t.options.scope?.id}function H(t){return{onFetch:(e,s)=>{const i=e.options,r=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const c=async()=>{let s=!1;const c=w(e.options,e.fetchOptions),h=async(t,i,r)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);const n=(()=>{const t={client:e.client,queryKey:e.queryKey,pageParam:i,direction:r?"backward":"forward",meta:e.options.meta};var n;return n=t,Object.defineProperty(n,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)}),t})(),a=await c(n),{maxPages:o}=e.options,u=r?C:O;return{pages:u(t.pages,a,o),pageParams:u(t.pageParams,i,o)}};if(r&&n.length){const t="backward"===r,e={pages:n,pageParams:a},s=(t?G:_)(i,e);o=await h(e,s,t)}else{const e=t??n.length;do{const t=0===u?a[0]??i.initialPageParam:_(i,o);if(u>0&&null==t)break;o=await h(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function _(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function G(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}var B=class{#g;#y;#h;#O;#C;#R;#w;#S;constructor(t={}){this.#g=t.queryCache||new j,this.#y=t.mutationCache||new K,this.#h=t.defaultOptions||{},this.#O=new Map,this.#C=new Map,this.#R=0}mount(){this.#R++,1===this.#R&&(this.#w=S.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#S=Q.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#R--,0===this.#R&&(this.#w?.(),this.#w=void 0,this.#S?.(),this.#S=void 0)}isFetching(t){return this.#g.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#y.findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=this.#g.build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(o(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return this.#g.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),r=this.#g.get(i.queryHash),n=r?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,n);if(void 0!==a)return this.#g.build(this,i).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return x.batch(()=>this.#g.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state}removeQueries(t){const e=this.#g;x.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){const s=this.#g;return x.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const s={revert:!0,...e},i=x.batch(()=>this.#g.findAll(t).map(t=>t.cancel(s)));return Promise.all(i).then(r).catch(r)}invalidateQueries(t,e={}){return x.batch(()=>(this.#g.findAll(t).forEach(t=>{t.invalidate()}),"none"===t?.refetchType?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e)))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=x.batch(()=>this.#g.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(r)),"paused"===t.state.fetchStatus?Promise.resolve():e}));return Promise.all(i).then(r)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=this.#g.build(this,e);return s.isStaleByTime(o(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(r).catch(r)}fetchInfiniteQuery(t){return t.behavior=H(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(r).catch(r)}ensureInfiniteQueryData(t){return t.behavior=H(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Q.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#h}setDefaultOptions(t){this.#h=t}setQueryDefaults(t,e){this.#O.set(d(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#O.values()],s={};return e.forEach(e=>{f(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#C.set(d(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#C.values()],s={};return e.forEach(e=>{f(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...this.#h.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=l(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===R&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#h.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}},N=class extends s{constructor(t,e){super(),this.options=e,this.#u=t,this.#Q=null,this.#q=q(),this.options.experimental_prefetchInRender||this.#q.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#u;#P=void 0;#F=void 0;#E=void 0;#D;#T;#q;#Q;#I;#x;#A;#M;#U;#j;#k=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#P.addObserver(this),W(this.#P,this.options)?this.#K():this.updateResult(),this.#L())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return z(this.#P,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return z(this.#P,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#H(),this.#_(),this.#P.removeObserver(this)}setOptions(t){const e=this.options,s=this.#P;if(this.options=this.#u.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof u(this.options.enabled,this.#P))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#G(),this.#P.setOptions(this.options),e._defaulted&&!y(this.options,e)&&this.#u.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#P,observer:this});const i=this.hasListeners();i&&$(this.#P,s,this.options,e)&&this.#K(),this.updateResult(),!i||this.#P===s&&u(this.options.enabled,this.#P)===u(e.enabled,this.#P)&&o(this.options.staleTime,this.#P)===o(e.staleTime,this.#P)||this.#B();const r=this.#N();!i||this.#P===s&&u(this.options.enabled,this.#P)===u(e.enabled,this.#P)&&r===this.#j||this.#W(r)}getOptimisticResult(t){const e=this.#u.getQueryCache().build(this.#u,t),s=this.createResult(e,t);return function(t,e){if(!y(t.getCurrentResult(),e))return!0;return!1}(this,s)&&(this.#E=s,this.#T=this.options,this.#D=this.#P.state),s}getCurrentResult(){return this.#E}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#k.add(t)}getCurrentQuery(){return this.#P}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#u.defaultQueryOptions(t),s=this.#u.getQueryCache().build(this.#u,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#K({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#E))}#K(t){this.#G();let e=this.#P.fetch(this.options,t);return t?.throwOnError||(e=e.catch(r)),e}#B(){this.#H();const t=o(this.options.staleTime,this.#P);if(i||this.#E.isStale||!n(t))return;const e=a(this.#E.dataUpdatedAt,t)+1;this.#M=setTimeout(()=>{this.#E.isStale||this.updateResult()},e)}#N(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#P):this.options.refetchInterval)??!1}#W(t){this.#_(),this.#j=t,!i&&!1!==u(this.options.enabled,this.#P)&&n(this.#j)&&0!==this.#j&&(this.#U=setInterval(()=>{(this.options.refetchIntervalInBackground||S.isFocused())&&this.#K()},this.#j))}#L(){this.#B(),this.#W(this.#N())}#H(){this.#M&&(clearTimeout(this.#M),this.#M=void 0)}#_(){this.#U&&(clearInterval(this.#U),this.#U=void 0)}createResult(t,e){const s=this.#P,i=this.options,r=this.#E,n=this.#D,a=this.#T,o=t!==s?t.state:this.#F,{state:u}=t;let c,h={...u},l=!1;if(e._optimisticResults){const r=this.hasListeners(),n=!r&&W(t,e),a=r&&$(t,s,e,i);(n||a)&&(h={...h,...U(u.data,t.options)}),"isRestoring"===e._optimisticResults&&(h.fetchStatus="idle")}let{error:d,errorUpdatedAt:f,status:p}=h;c=h.data;let y=!1;if(void 0!==e.placeholderData&&void 0===c&&"pending"===p){let t;r?.isPlaceholderData&&e.placeholderData===a?.placeholderData?(t=r.data,y=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#A?.state.data,this.#A):e.placeholderData,void 0!==t&&(p="success",c=g(r?.data,t,e),l=!0)}if(e.select&&void 0!==c&&!y)if(r&&c===n?.data&&e.select===this.#I)c=this.#x;else try{this.#I=e.select,c=e.select(c),c=g(r?.data,c,e),this.#x=c,this.#Q=null}catch(w){this.#Q=w}this.#Q&&(d=this.#Q,c=this.#x,f=Date.now(),p="error");const m="fetching"===h.fetchStatus,v="pending"===p,b="error"===p,O=v&&m,C=void 0!==c,R={status:p,fetchStatus:h.fetchStatus,isPending:v,isSuccess:"success"===p,isError:b,isInitialLoading:O,isLoading:O,data:c,dataUpdatedAt:h.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>o.dataUpdateCount||h.errorUpdateCount>o.errorUpdateCount,isFetching:m,isRefetching:m&&!v,isLoadingError:b&&!C,isPaused:"paused"===h.fetchStatus,isPlaceholderData:l,isRefetchError:b&&C,isStale:J(t,e),refetch:this.refetch,promise:this.#q};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===R.status?t.reject(R.error):void 0!==R.data&&t.resolve(R.data)},i=()=>{const t=this.#q=R.promise=q();e(t)},r=this.#q;switch(r.status){case"pending":t.queryHash===s.queryHash&&e(r);break;case"fulfilled":"error"!==R.status&&R.data===r.value||i();break;case"rejected":"error"===R.status&&R.error===r.reason||i()}}return R}updateResult(){const t=this.#E,e=this.createResult(this.#P,this.options);if(this.#D=this.#P.state,this.#T=this.options,void 0!==this.#D.data&&(this.#A=this.#P),y(e,t))return;this.#E=e;this.#z({listeners:(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#k.size)return!0;const i=new Set(s??this.#k);return this.options.throwOnError&&i.add("error"),Object.keys(this.#E).some(e=>{const s=e;return this.#E[s]!==t[s]&&i.has(s)})})()})}#G(){const t=this.#u.getQueryCache().build(this.#u,this.options);if(t===this.#P)return;const e=this.#P;this.#P=t,this.#F=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#L()}#z(t){x.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#E)}),this.#u.getQueryCache().notify({query:this.#P,type:"observerResultsUpdated"})})}};function W(t,e){return function(t,e){return!1!==u(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&z(t,e,e.refetchOnMount)}function z(t,e,s){if(!1!==u(e.enabled,t)&&"static"!==o(e.staleTime,t)){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&J(t,e)}return!1}function $(t,e,s,i){return(t!==e||!1===u(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&J(t,s)}function J(t,e){return!1!==u(e.enabled,t)&&t.isStaleByTime(o(e.staleTime,t))}var V=t.createContext(void 0),X=({client:s,children:i})=>(t.useEffect(()=>(s.mount(),()=>{s.unmount()}),[s]),e.jsx(V.Provider,{value:s,children:i})),Y=t.createContext(!1);Y.Provider;var Z=t.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),tt=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function et(e,s,n){const a=t.useContext(Y),o=t.useContext(Z),u=(()=>{const e=t.useContext(V);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e})(),c=u.defaultQueryOptions(e);u.getDefaultOptions().queries?._experimental_beforeQuery?.(c),c._optimisticResults=a?"isRestoring":"optimistic",(t=>{if(t.suspense){const e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}})(c),((t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))})(c,o),(e=>{t.useEffect(()=>{e.clearReset()},[e])})(o);const h=!u.getQueryCache().get(c.queryHash),[l]=t.useState(()=>new s(u,c)),d=l.getOptimisticResult(c),f=!a&&!1!==e.subscribed;if(t.useSyncExternalStore(t.useCallback(t=>{const e=f?l.subscribe(x.batchCalls(t)):r;return l.updateResult(),e},[l,f]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),t.useEffect(()=>{l.setOptions(c)},[c,l]),((t,e)=>t?.suspense&&e.isPending)(c,d))throw tt(c,l,o);if((({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:r})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(r&&void 0===t.data||function(t,e){return"function"==typeof t?t(...e):!!t}(s,[t.error,i])))({result:d,errorResetBoundary:o,throwOnError:c.throwOnError,query:u.getQueryCache().get(c.queryHash),suspense:c.suspense}))throw d.error;if(u.getDefaultOptions().queries?._experimental_afterQuery?.(c,d),c.experimental_prefetchInRender&&!i&&((t,e)=>t.isLoading&&t.isFetching&&!e)(d,a)){const t=h?tt(c,l,o):u.getQueryCache().get(c.queryHash)?.promise;t?.catch(r).finally(()=>{l.updateResult()})}return c.notifyOnChangeProps?d:l.trackResult(d)}function st(t,e){return et(t,N)}export{B as Q,X as a,st as u};
