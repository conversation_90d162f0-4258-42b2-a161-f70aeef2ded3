import{W as t,i,N as r}from"./index-BNatCL7N.js";import"./ui-B-v3Zwus.js";import"./router-B-XVe37S.js";import"./vendor-DEQ385Nk.js";import"./query-NIfa3cXZ.js";import"./icons-DaMsayX9.js";class e extends t{constructor(){super(...arguments),this.selectionStarted=!1}async impact(t){const i=this.patternForImpact(null==t?void 0:t.style);this.vibrateWithPattern(i)}async notification(t){const i=this.patternForNotification(null==t?void 0:t.type);this.vibrateWithPattern(i)}async vibrate(t){const i=(null==t?void 0:t.duration)||300;this.vibrateWithPattern([i])}async selectionStart(){this.selectionStarted=!0}async selectionChanged(){this.selectionStarted&&this.vibrateWithPattern([70])}async selectionEnd(){this.selectionStarted=!1}patternForImpact(t=i.Heavy){return t===i.Medium?[43]:t===i.Light?[20]:[61]}patternForNotification(t=r.Success){return t===r.Warning?[30,40,30,50,60]:t===r.Error?[27,45,50]:[35,65,21]}vibrateWithPattern(t){if(!navigator.vibrate)throw this.unavailable("Browser does not support the vibrate API");navigator.vibrate(t)}}export{e as HapticsWeb};
