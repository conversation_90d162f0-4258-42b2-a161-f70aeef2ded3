import{m as a,j as e,n as s,o as t,p as o,q as d,O as n,D as l}from"./ui-B-v3Zwus.js";import{r as i}from"./router-B-XVe37S.js";import{h as r}from"./index-BNatCL7N.js";import{X as c}from"./icons-DaMsayX9.js";const m=a,f=s,p=i.forwardRef(({className:a,...s},t)=>e.jsx(n,{ref:t,className:r("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...s}));p.displayName=n.displayName;const u=i.forwardRef(({className:a,children:s,...d},n)=>e.jsxs(f,{children:[e.jsx(p,{}),e.jsxs(t,{ref:n,className:r("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...d,children:[s,e.jsxs(o,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=t.displayName;const g=({className:a,...s})=>e.jsx("div",{className:r("flex flex-col space-y-1.5 text-center sm:text-left",a),...s});g.displayName="DialogHeader";const x=i.forwardRef(({className:a,...s},t)=>e.jsx(d,{ref:t,className:r("text-lg font-semibold leading-none tracking-tight",a),...s}));x.displayName=d.displayName;i.forwardRef(({className:a,...s},t)=>e.jsx(l,{ref:t,className:r("text-sm text-muted-foreground",a),...s})).displayName=l.displayName;export{m as D,u as a,g as b,x as c};
