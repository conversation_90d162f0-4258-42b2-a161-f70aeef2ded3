version: '3.8'

services:
  auto-scouter-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-https://api.yourdomain.com/api/v1}
        VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0}
        VITE_APP_ENVIRONMENT: production
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NGINX_HOST=${DOMAIN:-yourdomain.com}
    volumes:
      # Mount SSL certificates (optional)
      - ./ssl:/etc/nginx/ssl:ro
      # Mount custom nginx config (optional)
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.auto-scouter.rule=Host(`${DOMAIN:-yourdomain.com}`)"
      - "traefik.http.routers.auto-scouter.tls=true"
      - "traefik.http.routers.auto-scouter.tls.certresolver=letsencrypt"

networks:
  default:
    name: auto-scouter-network
