# Production Environment Configuration for Auto Scouter Frontend

# API Configuration
VITE_API_BASE_URL=https://api.autoscouter.com/api/v1
VITE_API_TIMEOUT=10000

# Application Configuration
VITE_APP_NAME=Auto Scouter
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Configuration
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_STRICT_CSP=true

# External Services (Replace with actual values)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Cache Configuration
VITE_CACHE_VERSION=1.0.0

# Build Configuration
VITE_BUILD_TIMESTAMP=${BUILD_TIMESTAMP}
VITE_GIT_COMMIT=${GIT_COMMIT}
