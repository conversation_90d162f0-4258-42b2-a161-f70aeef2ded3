# Environment Variables Template for Auto Scouter Frontend
# Copy this file to .env.production and fill in the actual values

# API Configuration - REQUIRED
VITE_API_BASE_URL=https://your-api-domain.com/api/v1
VITE_API_TIMEOUT=10000

# Application Configuration
VITE_APP_NAME=Auto Scouter
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Configuration
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_STRICT_CSP=true

# External Services - CONFIGURE THESE
# Google Analytics (optional)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Sentry Error Reporting (optional)
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Cache Configuration
VITE_CACHE_VERSION=1.0.0

# Build Information (automatically set during CI/CD)
VITE_BUILD_TIMESTAMP=
VITE_GIT_COMMIT=
