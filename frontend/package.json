{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:production": "NODE_ENV=production npm run build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "cap:init": "cap init", "cap:add:android": "cap add android", "cap:sync": "cap sync", "cap:build": "npm run build && cap sync", "cap:open:android": "cap open android", "android:build": "npm run build && cap sync android && cap build android", "android:run": "npm run android:build && cap run android"}, "dependencies": {"@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/haptics": "^6.0.0", "@capacitor/keyboard": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@capacitor/splash-screen": "^6.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^2.30.0", "express": "^5.1.0", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@capacitor/cli": "^6.0.0", "@types/node": "^20.19.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "ts-jest": "^29.4.0", "typescript": "^5.6.3", "vite": "^7.0.0"}}