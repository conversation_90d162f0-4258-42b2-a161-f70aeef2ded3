{"name": "auto_scouter", "version": "1.0.0", "description": "Automated scouting system for robotics competitions", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && pip install -r requirements.txt", "build": "cd frontend && npm run build", "setup": "npm run install:all && cd backend && python create_db.py"}, "devDependencies": {"concurrently": "^8.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/ezekaj/auto_scouter.git"}, "keywords": ["robotics", "scouting", "react", "<PERSON><PERSON><PERSON>", "typescript"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ezekaj/auto_scouter/issues"}, "homepage": "https://github.com/ezekaj/auto_scouter#readme"}