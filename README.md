# Vehicle Scout

An automated vehicle scouting and monitoring system with 24/7 web scraping capabilities, built with React (frontend) and FastAPI (backend).

## 🚀 Features

- **Vehicle Search**: Advanced search and filtering of scraped vehicles
- **Real-time Updates**: Live vehicle data updates via Server-Sent Events (SSE)
- **24/7 Web Scraping**: Automated scraping from automotive websites
- **Alert System**: Custom alerts for vehicle matches based on user criteria
- **Dashboard**: Comprehensive system monitoring and analytics
- **Notification Center**: Real-time notifications for new matches and system events
- **Modern UI**: Responsive React interface with TypeScript and Tailwind CSS
- **RESTful API**: FastAPI backend with automatic documentation and OpenAPI spec

## 🏗️ Project Structure

```
auto_scouter/
├── frontend/          # React + TypeScript frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── services/      # API services
│   │   ├── hooks/         # Custom React hooks
│   │   ├── lib/           # Utilities and configurations
│   │   └── types/         # TypeScript type definitions
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
├── backend/           # FastAPI backend
│   ├── app/
│   │   ├── core/          # Configuration and settings
│   │   ├── models/        # SQLAlchemy database models
│   │   ├── routers/       # API endpoint routers
│   │   ├── schemas/       # Pydantic schemas
│   │   ├── services/      # Business logic services
│   │   ├── scraper/       # Web scraping modules
│   │   └── tasks/         # Background tasks
│   ├── tests/             # Test suite
│   ├── requirements.txt
│   └── setup_scraper.py   # Setup and initialization script
└── README.md
```

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern UI library with hooks
- **TypeScript** - Type-safe JavaScript development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Query** - Data fetching and caching
- **React Router** - Client-side routing
- **Lucide React** - Beautiful icon library

### Backend
- **FastAPI** - High-performance Python web framework
- **SQLAlchemy** - SQL toolkit and ORM
- **SQLite/PostgreSQL** - Database support (SQLite for dev, PostgreSQL for production)
- **Pydantic** - Data validation using Python type annotations
- **Uvicorn** - ASGI server
- **Celery** - Distributed task queue for background jobs
- **Redis** - In-memory data store for caching and task queuing
- **BeautifulSoup** - Web scraping and HTML parsing
- **APScheduler** - Advanced Python Scheduler for automated tasks

## 📋 Prerequisites

- **Node.js** (v18 or higher) - JavaScript runtime
- **Python** (v3.8 or higher) - Backend runtime
- **npm** or **yarn** - Package manager for frontend
- **pip** - Package manager for Python
- **Redis** (optional) - For background tasks and caching
- **PostgreSQL** (optional) - For production database

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/ezekaj/auto_scouter.git
cd auto_scouter
```

### 2. Backend Setup

```bash
cd backend

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt

# Run setup script to initialize database and create sample data
python setup_scraper.py

# Start the backend server
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The backend will be available at: http://localhost:8000

### 3. Frontend Setup

```bash
cd frontend

# Install Node.js dependencies
npm install

# Build the frontend for production
npm run build

# Serve the built frontend
npx serve -s dist -l 3000
```

The frontend will be available at: http://localhost:3000

### 4. Development Mode (Alternative)

For development with hot reload:

```bash
cd frontend
npm run dev
```

**Note**: If you encounter Node.js compatibility issues with the dev server, use the production build method above.

## 🔧 Development

### Running Both Services

For development, you'll need to run both the frontend and backend simultaneously:

1. **Terminal 1** (Backend):
   ```bash
   cd backend
   source venv/bin/activate  # Activate virtual environment
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Terminal 2** (Frontend):
   ```bash
   cd frontend
   npm run build && npx serve -s dist -l 3000
   ```

### API Documentation

When the backend is running, you can access:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health
- **System Status**: http://localhost:8000/api/v1/system/status

### Key API Endpoints

- `GET /api/v1/automotive/vehicles` - Search vehicles with filters
- `GET /api/v1/search/filters` - Get available search filters
- `POST /api/v1/automotive/scraper/trigger` - Trigger manual scraping
- `GET /api/v1/realtime/sse/system-status` - Real-time system status via SSE
- `GET /api/v1/realtime/sse/vehicle-matches` - Real-time vehicle matches via SSE

### Environment Variables

Create a `.env` file in the backend directory:

```env
DATABASE_URL=sqlite:///./auto_scouter.db
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 📊 Database Schema

### Core Models

- **VehicleListing**: Scraped vehicle data with specifications and pricing
- **User**: User accounts and authentication
- **Alert**: User-defined search alerts and criteria
- **Notification**: System notifications and user alerts
- **ScrapingJob**: Background scraping job tracking
- **SystemLog**: System activity and error logging

### Key Features

- **Real-time Updates**: Server-Sent Events for live data streaming
- **Background Processing**: Celery tasks for automated scraping
- **Alert Matching**: Automatic matching of vehicles to user alerts
- **Multi-source Scraping**: Support for multiple automotive websites

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add: amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Message Format

- `Add: new feature or functionality`
- `Edit: modifications to existing code`
- `Fix: bug fixes`
- `Remove: deleted code or features`

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [API documentation](http://localhost:8000/docs) when backend is running
2. Review the console logs for error messages
3. Create an issue on GitHub

## 🧪 Testing

### Backend Tests

```bash
cd backend
python -m pytest tests/ -v
```

### Frontend Tests

```bash
cd frontend
npm test
```

## 🚀 Production Deployment

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Deployment

1. Set up PostgreSQL database
2. Configure Redis for background tasks
3. Set production environment variables
4. Build frontend: `npm run build`
5. Deploy backend with production ASGI server

## 🔮 Future Enhancements

- [ ] Advanced user authentication and role-based access
- [ ] Mobile app support (React Native)
- [ ] Advanced analytics dashboard with charts
- [ ] Multi-language support
- [ ] Email notifications for alerts
- [ ] Vehicle comparison features
- [ ] Price history tracking
- [ ] Machine learning for price predictions
- [ ] API rate limiting and throttling
- [ ] Comprehensive logging and monitoring
